# CHANGE: [2025-06-19] 添加全局网络和DNS配置来解决Docker Hub连接问题
x-common-dns: &common-dns
  dns:
    - 8.8.8.8
    - 8.8.4.4
    - 223.5.5.5
    - 119.29.29.29

services:
  # Nginx反向代理服务
  nginx:
    image: nginx:alpine
    container_name: archive-flow-nginx
    ports:
      - "80:80"
    volumes:
      - ./frontend/config/nginx.conf:/etc/nginx/conf.d/default.conf
      - ./media:/var/www/html/media # 媒体文件共享
      - ./static:/var/www/html/static # 静态文件共享
    depends_on:
      web:
        # 修改nginx对web的依赖，等待web健康
        condition: service_healthy
      frontend:
        condition: service_started # frontend通常较快，或根据其特性调整
    restart: unless-stopped
    networks:
      - app-network
    <<: *common-dns

  redis:
    image: "redis:alpine"
    container_name: archive-flow-redis
    <<: *common-dns
    ports:
      # 暴露到主机以便调试，可选
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      # 为redis添加一个简单的健康检查
      test: [ "CMD", "redis-cli", "ping" ]
      interval: 10s
      timeout: 5s
      retries: 5

  # CHANGE: [日期] 添加PostgreSQL数据库服务
  db:
    image: postgres:13-alpine
    container_name: archive-flow-postgres
    <<: *common-dns
    volumes:
      - postgres_data:/var/lib/postgresql/data
    environment:
      - POSTGRES_DB=archivedb
      - POSTGRES_USER=archiveuser
      - POSTGRES_PASSWORD=1234QWER!
    ports:
      # 可选: 暴露到主机以便直接访问数据库进行调试
      - "5432:5432"
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      # PostgreSQL官方镜像通常内置了健康检查，这里显式定义一个pg_isready
      test: [ "CMD-SHELL", "pg_isready -U archiveuser -d archivedb -h localhost" ]
      interval: 10s
      timeout: 5s
      retries: 5

  web:
    build: 
      context: .
      args:
        # CHANGE: [2025-06-29] 支持自定义用户ID以解决开发环境权限问题
        # 在Linux/Mac上可以设置为当前用户ID: USER_ID=$(id -u) GROUP_ID=$(id -g)
        USER_ID: ${USER_ID:-1000}
        GROUP_ID: ${GROUP_ID:-1000}
    container_name: archive-flow-backend
    <<: *common-dns
    # CHANGE: [2025-06-30] 修复chown权限问题。容器需以root启动才能执行chown，
    # 然后再切换回appuser用户运行应用，确保安全。
    user: root
    command: >
      sh -c "chown -R appuser:appuser /app/media /app/static &&
             exec su -s /bin/sh -c 'python manage.py migrate && python manage.py runserver 0.0.0.0:8000' appuser"
    volumes:
      - .:/app
      # CHANGE: [2025-06-30] 为media和static目录创建命名卷，以更好地管理权限和数据持久化
      # - ./media:/app/media
      # - ./static:/app/static
      - media_data:/app/media
      - static_data:/app/static
    ports:
      - "8000:8000"
    environment:
      # 这些环境变量会覆盖 settings.py 中的值
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
      # CHANGE: [日期] 更新web服务以使用PostgreSQL
      - DATABASE_ENGINE=django.db.backends.postgresql
      - DATABASE_NAME=archivedb
      - DATABASE_USER=archiveuser
      - DATABASE_PASSWORD=1234QWER!
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      # 设置权限模式为debug，使用superuser账户进行开发和测试
      - PERMISSION_MODE=debug
      # CHANGE: [2025-06-19] 强制所有AI库使用CPU模式
      - CUDA_VISIBLE_DEVICES=""
      - PADDLEPADDLE_USE_GPU=0
      - USE_CUDA=0
    depends_on:
      # CHANGE: [日期] 添加db依赖
      redis:
        condition: service_healthy
      db:
        condition: service_healthy
    networks:
      - app-network
    restart: unless-stopped
    healthcheck:
      # 为web服务添加健康检查
      # 使用curl检查Django服务是否响应(不使用-f参数，允许任何状态码)
      test: ["CMD", "curl", "http://localhost:8000/"]
      interval: 30s # 检查间隔增加到30秒
      timeout: 15s # 超时时间增加到15秒
      retries: 3 # 重试次数
      start_period: 120s # 启动期

  # CHANGE: [2025-07-03] 重构worker配置，分离不同类型的任务处理
  # 默认worker - 处理一般任务
  worker-default:
    build:
      context: .
      args:
        USER_ID: ${USER_ID:-1000}
        GROUP_ID: ${GROUP_ID:-1000}
    container_name: archive-flow-worker-default
    <<: *common-dns
    user: root
    command: >
      sh -c "chown -R appuser:appuser /app/media /app/static &&
             exec su -s /bin/sh -c 'celery -A archive_flow_manager worker --loglevel=info --queues=default,celery --concurrency=4 --hostname=default@%h' appuser"
    volumes:
      - .:/app
      - media_data:/app/media
      - static_data:/app/static
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
      - DATABASE_ENGINE=django.db.backends.postgresql
      - DATABASE_NAME=archivedb
      - DATABASE_USER=archiveuser
      - DATABASE_PASSWORD=1234QWER!
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - PERMISSION_MODE=debug
      - CUDA_VISIBLE_DEVICES=""
      - PADDLEPADDLE_USE_GPU=0
      - USE_CUDA=0
    depends_on:
      redis:
        condition: service_healthy
      db:
        condition: service_healthy
      web:
        condition: service_healthy
    networks:
      - app-network
    restart: unless-stopped

  # PDF处理专用worker - 限制并发数以控制资源使用
  worker-pdf:
    build:
      context: .
      args:
        USER_ID: ${USER_ID:-1000}
        GROUP_ID: ${GROUP_ID:-1000}
    container_name: archive-flow-worker-pdf
    <<: *common-dns
    user: root
    command: >
      sh -c "chown -R appuser:appuser /app/media /app/static &&
             exec su -s /bin/sh -c 'celery -A archive_flow_manager worker --loglevel=info --queues=pdf_processing --concurrency=2 --hostname=pdf@%h' appuser"
    volumes:
      - .:/app
      - media_data:/app/media
      - static_data:/app/static
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
      - DATABASE_ENGINE=django.db.backends.postgresql
      - DATABASE_NAME=archivedb
      - DATABASE_USER=archiveuser
      - DATABASE_PASSWORD=1234QWER!
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - PERMISSION_MODE=debug
      - CUDA_VISIBLE_DEVICES=""
      - PADDLEPADDLE_USE_GPU=0
      - USE_CUDA=0
    depends_on:
      redis:
        condition: service_healthy
      db:
        condition: service_healthy
      web:
        condition: service_healthy
    networks:
      - app-network
    restart: unless-stopped

  # CHANGE: [2025-05-17] 添加 Celery Beat 服务用于定时任务
  beat:
    build:
      context: .
      args:
        USER_ID: ${USER_ID:-1000}
        GROUP_ID: ${GROUP_ID:-1000}
    container_name: archive-flow-beat
    <<: *common-dns
    user: root
    command: >
      sh -c "chown -R appuser:appuser /app/media /app/static &&
             exec su -s /bin/sh -c 'celery -A archive_flow_manager beat -l info --scheduler django_celery_beat.schedulers:DatabaseScheduler' appuser"
    volumes:
      - .:/app
      - media_data:/app/media
      - static_data:/app/static
    environment:
      - CELERY_BROKER_URL=redis://redis:6379/0
      - CELERY_RESULT_BACKEND=redis://redis:6379/1
      - DATABASE_ENGINE=django.db.backends.postgresql
      - DATABASE_NAME=archivedb
      - DATABASE_USER=archiveuser
      - DATABASE_PASSWORD=1234QWER!
      - DATABASE_HOST=db
      - DATABASE_PORT=5432
      - PERMISSION_MODE=debug
      - CUDA_VISIBLE_DEVICES=""
      - PADDLEPADDLE_USE_GPU=0
      - USE_CUDA=0
    depends_on:
      redis:
        condition: service_healthy
      db:
        condition: service_healthy
      web:
        condition: service_healthy
      # CHANGE: [2025-07-03] 等待所有worker启动后再启动beat
      worker-default:
        condition: service_started
      worker-pdf:
        condition: service_started
    networks:
      - app-network
    restart: unless-stopped
  # END CHANGE

  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
    container_name: archive-flow-frontend
    <<: *common-dns
    volumes:
      - ./frontend:/app
      # 排除 node_modules 和 .next 目录的挂载
      - /app/node_modules
      - /app/.next
    ports:
      - "3000:3000"
    environment:
      # 环境变量配置
      - NODE_ENV=development # 设置Node环境为开发模式
      # API访问配置 - 统一通过Nginx代理访问
      - NEXT_PUBLIC_API_URL=http://127.0.0.1 # 浏览器端通过Nginx代理访问
      - INTERNAL_API_URL=http://nginx # 服务器端也通过Nginx代理访问，确保路径一致
      # NextAuth.js 配置
      - NEXTAUTH_URL=http://127.0.0.1
      - NEXTAUTH_SECRET=your-super-secret-nextauth-key-min-32-chars-long
      - DJANGO_ACCESS_TOKEN_LIFETIME_SECONDS=86400
      # 其他前端配置
      - NEXT_PUBLIC_DEBUG=true # 开启调试功能
      - NEXT_PUBLIC_ANALYTICS_ENABLED=false # 禁用分析服务
      #AG Grid许可证拦截配置
      - NEXT_PUBLIC_ENABLE_AG_GRID_ERROR_INTERCEPT=true
      - NEXT_PUBLIC_AG_GRID_INTERCEPT_DEBUG=false
    depends_on:
      web:
        condition: service_healthy
    networks:
      - app-network
    restart: unless-stopped

networks:
  app-network:
    driver: bridge

volumes:
  redis_data:
  # CHANGE: [日期] 添加PostgreSQL数据卷
  postgres_data:
  # CHANGE: [2025-06-30] 定义media和static的命名卷
  media_data:
  static_data:
