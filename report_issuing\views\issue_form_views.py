# report_issuing/views/issue_form_views.py
import logging
from rest_framework import viewsets, status
from rest_framework.response import Response
from rest_framework.permissions import IsAuthenticated
from rest_framework.decorators import action
from django.db import transaction
from django.shortcuts import get_object_or_404

from report_issuing.models import IssueForm
from archive_records.models import ArchiveRecord
from ..serializers import IssueFormSerializer, IssueFormItemSerializer, DraftUpdateSerializer
from report_issuing.services.stateless.issue_initialization_service import IssueInitializationService, InvalidOperationError
from report_issuing.services.query_services.issue_query_service import IssueQueryService
from report_issuing.form_state_machine import FormStateMachine
from report_issuing.state_handlers.base_handler import InvalidTransitionError

logger = logging.getLogger(__name__)

class IssueFormViewSet(viewsets.ModelViewSet):
    """
    发放单视图集
    
    提供发放单的创建、查询以及相关操作的API端点。
    使用业务标识符(issue_number)作为资源标识符，符合RESTful最佳实践。
    """
    queryset = IssueForm.objects.filter(is_deleted=False).order_by('-created_at')
    serializer_class = IssueFormSerializer
    permission_classes = [IsAuthenticated]
    
    # 使用业务标识符作为查找字段
    lookup_field = 'issue_number'
    lookup_url_kwarg = 'issue_number'

    def create(self, request, *args, **kwargs):
        """
        创建一个新的草稿发放单及其初始条目。
        
        请求体 (body) 示例:
        {
            "form_data": {
                "issue_number": "ISSUE-2024-001",
                "issue_date": "2024-07-26T10:00:00Z",
                "receiver_name": "张三",
                "receiver_unit": "测试单位",
                "receiver_phone": "13800138000",
                "notes": "这是一个测试发放单"
            },
            "items_data": [
                {"archive_record_id": 1, "copies": 1},
                {"archive_record_id": 2, "copies": 3}
            ]
        }
        """
        form_data = request.data.get('form_data')
        items_data = request.data.get('items_data')
        
        if not form_data or not items_data:
            return Response({'error': '请求体必须包含 form_data 和 items_data'}, status=status.HTTP_400_BAD_REQUEST)
            
        try:
            # 初始化服务
            initialization_service = IssueInitializationService(user_id=request.user.id)
            # 调用服务创建草稿单
            new_form = initialization_service.create_draft_issue_form_with_items(form_data, items_data)
            # 使用专用的简化序列化器返回与请求格式一致的响应
            serializer = DraftUpdateSerializer(new_form)
            return Response(serializer.data, status=status.HTTP_201_CREATED)
            
        except InvalidOperationError as e:
            logger.warning(f"创建发放单失败 (业务校验失败): {e}")
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"创建发放单时发生未知错误: {e}", exc_info=True)
            return Response({'error': '服务器内部错误'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def update(self, request, *args, **kwargs):
        """
        更新一个草稿状态的发放单。
        这会通过状态机来调用事务性的更新服务。
        """
        try:
            instance = self.get_object()
            form_id = instance.id
            user_id = request.user.id
            
            # 初始化状态机
            state_machine = FormStateMachine(form_id=form_id, user_id=user_id)
            
            # 解析请求数据
            form_data = request.data.get('form_data', {})
            items_data = request.data.get('items_data', None) # 接收完整的条目列表

            # 调用状态机执行更新
            updated_form = state_machine.update_draft(form_data, items_data)
            
            # 使用专用的简化序列化器返回与请求格式一致的响应
            serializer = DraftUpdateSerializer(updated_form)
            return Response(serializer.data, status=status.HTTP_200_OK)

        except InvalidTransitionError as e:
            logger.warning(f"更新发放单失败 (状态不允许): {e}")
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"更新发放单时发生未知错误: {e}", exc_info=True)
            return Response({'error': '服务器内部错误'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    def partial_update(self, request, *args, **kwargs):
        """ PATCH请求也路由到我们的自定义更新逻辑 """
        return self.update(request, *args, **kwargs)

    @action(detail=False, methods=['get'], url_path='issuable-archives')
    def issuable_archives(self, request):
        """
        查询可用于创建或编辑发放单的档案条目。
        
        支持的查询参数 (query params):
        - unified_number
        - sample_number
        - client_unit
        - client_name
        - commission_datetime_start (ISO format)
        - commission_datetime_end (ISO format)
        - project_number
        - project_name
        - project_location
        """
        try:
            query_service = IssueQueryService(user_id=request.user.id)
            search_params = request.query_params.dict()
            archives = query_service.get_issuable_archives(search_params)
            return Response(archives, status=status.HTTP_200_OK)
        except Exception as e:
            logger.error(f"查询可发放档案时发生未知错误: {e}", exc_info=True)
            return Response({'error': '服务器内部错误'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=False, methods=['patch'], url_path='update-total-copies')
    def update_total_copies_unified(self, request):
        """
        统一更新档案记录的总份数（支持单个和批量）
        
        PATCH /api/report-issuing/issue-forms/update-total-copies/
        
        请求体:
        {
            "archive_ids": ["ARCHIVE-2024-001"],           // 单个更新
            "total_issue_copies": 5
        }
        
        或
        
        {
            "archive_ids": ["ARCHIVE-2024-001", "ARCHIVE-2024-002"],  // 批量更新
            "total_issue_copies": 5
        }
        
        返回:
        成功情况（所有记录都更新成功）:
        {
            "success": true,
            "data": {
                "updated_count": 2,
                "total_requested": 2,
                "errors": []
            }
        }
        
        失败情况（部分或全部记录更新失败）:
        {
            "success": false,
            "data": {
                "updated_count": 1,
                "total_requested": 3,
                "errors": [
                    {
                        "archive_id": "ARCHIVE-2024-003",
                        "error": "档案记录不存在: ARCHIVE-2024-003"
                    },
                    {
                        "archive_id": "ARCHIVE-2024-004", 
                        "error": "新总份数(2)不能小于已发放份数(5)"
                    }
                ]
            }
        }
        """
        try:
            # 验证输入参数
            archive_ids = request.data.get('archive_ids', [])
            new_total_copies = request.data.get('total_issue_copies')
            
            if not archive_ids or not isinstance(archive_ids, list):
                return Response({
                    'error': '缺少必需参数: archive_ids (数组)'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            if new_total_copies is None:
                return Response({
                    'error': '缺少必需参数: total_issue_copies'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 验证是否为正整数
            try:
                new_total_copies = int(new_total_copies)
                if new_total_copies <= 0:
                    raise ValueError()
            except (ValueError, TypeError):
                return Response({
                    'error': 'total_issue_copies 必须是大于0的整数'
                }, status=status.HTTP_400_BAD_REQUEST)
            
            # 第一阶段：验证所有记录（不进行任何更新）
            validation_errors = []
            archive_records = []
            
            for archive_id in archive_ids:
                try:
                    # 根据archive_id查找档案记录 (使用统一编号)
                    archive_record = ArchiveRecord.objects.get(unified_number=archive_id)
                    
                    # 计算已发放份数
                    first_issued = archive_record.first_issue_copies or 0
                    second_issued = archive_record.second_issue_copies or 0
                    total_issued = first_issued + second_issued
                    
                    # 验证新总份数必须 >= 已发放份数
                    if new_total_copies < total_issued:
                        validation_errors.append({
                            'archive_id': archive_id,
                            'error': f'新总份数({new_total_copies})不能小于已发放份数({total_issued})'
                        })
                    else:
                        archive_records.append(archive_record)
                        
                except ArchiveRecord.DoesNotExist:
                    validation_errors.append({
                        'archive_id': archive_id,
                        'error': f'档案记录不存在: {archive_id}'
                    })
                except Exception as e:
                    validation_errors.append({
                        'archive_id': archive_id,
                        'error': f'验证失败: {str(e)}'
                    })
            
            # 如果有任何验证错误，直接返回失败，不进行任何更新
            if validation_errors:
                return Response({
                    'success': False,
                    'data': {
                        'updated_count': 0,
                        'errors': validation_errors,
                        'total_requested': len(archive_ids)
                    }
                }, status=status.HTTP_200_OK)
            
            # 第二阶段：所有验证通过，使用事务进行批量更新
            updated_count = 0
            with transaction.atomic():
                for archive_record in archive_records:
                    archive_record.total_issue_copies = new_total_copies
                    archive_record.save(update_fields=['total_issue_copies'])
                    updated_count += 1
                    logger.info(f"批量更新档案 {archive_record.unified_number} 总份数: -> {new_total_copies}")
            
            # 所有记录都成功更新
            is_batch_success = True
            
            return Response({
                'success': is_batch_success,
                'data': {
                    'updated_count': updated_count,
                    'errors': [],  # 成功情况下没有错误
                    'total_requested': len(archive_ids)
                }
            }, status=status.HTTP_200_OK)
            
        except Exception as e:
            logger.error(f"批量更新总份数时发生错误: error={str(e)}", exc_info=True)
            return Response({
                'error': '服务器内部错误'
            }, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    @action(detail=True, methods=['post'], url_path='lock')
    def lock(self, request, issue_number=None):
        """ 锁定发放单 """
        try:
            # 获取发放单实例并使用其数据库ID
            instance = self.get_object()
            state_machine = FormStateMachine(form_id=instance.id, user_id=request.user.id)
            locked_form = state_machine.lock()
            serializer = self.get_serializer(locked_form)
            return Response(serializer.data, status=status.HTTP_200_OK)
        except InvalidTransitionError as e:
            return Response({'error': str(e)}, status=status.HTTP_400_BAD_REQUEST)
        except Exception as e:
            logger.error(f"锁定发放单 {issue_number} 时出错: {e}", exc_info=True)
            return Response({'error': '服务器内部错误'}, status=status.HTTP_500_INTERNAL_SERVER_ERROR)

    # 其他操作 (list, retrieve, update, destroy) 可以根据需要实现
    # 例如，默认的 list, retrieve 可能需要一个 Serializer
    # update 和 destroy 可能需要调用不同的业务服务 