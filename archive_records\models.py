import os
import uuid
import json
import logging
import datetime
from decimal import Decimal
from typing import Dict, Any, List, Optional

from django.db import models
from django.contrib.auth.models import User, Group
from django.utils import timezone
from django.conf import settings
from django.db.models import Count, Sum, Max
from django.core.exceptions import ValidationError
from datetime import timedelta

# Create your models here.

# 配置日志
logger = logging.getLogger(__name__)


# CHANGE: [2024-07-26] 添加会话状态枚举类，用于导入会话管理
class ImportSessionStatus(models.TextChoices):
    """Excel导入会话状态枚举"""

    # --- 中间处理状态 ---
    SELECT = "select", "选择文件"
    UPLOAD = "upload", "文件上传"
    ANALYSIS_START = "analysis_start", "分析开始"
    ANALYSIS_IN_PROGRESS = "analyzing", "分析中"
    ANALYSIS_COMPLETE = "analyzed", "分析完成"
    # REMOVE: [2025-06-01] 旧的冲突处理状态，将被新的状态系列取代
    # CONFLICT_RESOLUTION = "processing", "冲突处理" 
    
    # CHANGE: [2025-06-01] 新的冲突处理状态系列
    CONFLICT_RESOLUTION_STARTED = "conflict_resolution_started", "冲突处理已开始"
    CONFLICT_RESOLUTION_IN_PROGRESS = "conflict_resolution_in_progress", "冲突处理中"
    CONFLICT_RESOLUTION_PENDING = "conflict_resolution_pending", "冲突处理暂停"
    CONFLICT_RESOLUTION_COMPLETED = "conflict_resolution_completed", "冲突处理完成"
    # END CHANGE

    IMPORT_QUEUED = "queued", "排队等待导入"
    IMPORT_START = "import_start", "导入开始"
    IMPORT_IN_PROGRESS = "importing", "导入中"

    # --- 结果/错误展示状态 (具有特定"展示期") ---
    # CHANGE: [2024-07-29] 引入新的完成状态，取代旧的 IMPORT_COMPLETE
    IMPORT_COMPLETED_SUCCESSFULLY = "completed_successfully", "成功完成导入"
    IMPORT_COMPLETED_WITH_ERRORS = "completed_with_errors", "完成但有错误"
    
    # ERROR 用于流程性/系统性错误 (通常无ImportLog)
    ERROR = "error", "出错"

    # --- 短暂过渡状态 ---
    # CHANGE: [2024-07-29] CANCELLED 作为短暂过渡，最终导向 FINALIZED
    CANCELLED = "cancelled", "已取消"

    # --- 统一最终状态 ---
    # CHANGE: [2024-07-29] 引入 FINALIZED 作为统一最终状态，取代 ARCHIVED
    FINALIZED = "finalized", "已最终处理"

    # REMOVE: [2024-07-29] 移除 ARCHIVED 和旧的 IMPORT_COMPLETE
    # IMPORT_COMPLETE = "imported", "导入完成" # 旧状态，已由 IMPORT_COMPLETED_SUCCESSFULLY 替代
    # ARCHIVED = "archived", "已归档/数据固化" # 旧状态，已由 FINALIZED 替代


# CHANGE: [2024-07-26] 添加ImportSession模型，用于管理Excel导入会话
class ImportSession(models.Model):
    """Excel导入会话模型"""

    session_id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False)
    status = models.CharField(
        max_length=50, # CHANGE: [2025-06-01] 增加max_length以适应新的长状态名
        choices=ImportSessionStatus.choices,
        default=ImportSessionStatus.SELECT,
    )

    # 文件和分析信息
    file_name = models.CharField(max_length=255, blank=True, null=True)
    file_path = models.CharField(max_length=512, blank=True, null=True)

    sheet_count = models.IntegerField(default=0)
    record_count = models.IntegerField(default=0)
    conflict_count = models.IntegerField(default=0)

    # 进度信息
    progress = models.FloatField(default=0)  # 0-100
    current_sheet = models.IntegerField(default=0)
    current_record = models.IntegerField(default=0)

    # 用户和会话管理
    created_by = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.CASCADE,
        related_name="created_sessions",
    )
    processing_user = models.ForeignKey(
        settings.AUTH_USER_MODEL,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="processing_sessions",
    )

    # 时间戳
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)
    last_activity = models.DateTimeField(auto_now=True)
    last_heartbeat_at = models.DateTimeField(null=True, blank=True, db_index=True, verbose_name="最后心跳时间")
    expires_at = models.DateTimeField(null=True, blank=True)
    cancelled_at = models.DateTimeField(null=True, blank=True, verbose_name="取消时间")
    cleaned_up_at = models.DateTimeField(null=True, blank=True, verbose_name="异步清理完成时间")

    # CHANGE: [2024-07-29] 添加 results_display_expires_at 字段
    results_display_expires_at = models.DateTimeField(
        null=True, 
        blank=True, 
        verbose_name="结果展示过期时间"
    ) # 这个字段在现有代码的更下方，但根据搜索结果，它已存在，确保它在此处即可。

    # CHANGE: [2025-06-04] 添加分析统计字段，分离统计数据和错误信息 #issue_session_cancel_overwrite
    analysis_stats = models.JSONField(
        blank=True, 
        null=True, 
        verbose_name="分析统计数据",
        help_text="存储分析阶段的统计数据，格式: {'total': int, 'new': int, 'identical': int, 'update': int, 'error': int}"
    )

    # 错误信息
    error_message = models.TextField(blank=True, null=True)

    # 导入结果引用 - 成功完成导入后记录
    import_log = models.OneToOneField(
        "ImportLog",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="import_session",
    )

    # 定义终止状态和不应从终止状态回转的活动状态 (类属性)
    # CHANGE: [2025-05-28] 更新终止状态列表以反映新状态模型 (日期应为上次修改日期，但内容已确认)
    _CONCLUSIVE_STATUSES = [
        ImportSessionStatus.FINALIZED, # 新的统一最终状态
        ImportSessionStatus.CANCELLED, # 短暂过渡，但仍是活动的终结
        ImportSessionStatus.ERROR, # 流程性错误
        ImportSessionStatus.IMPORT_COMPLETED_SUCCESSFULLY, # 结果性状态
        ImportSessionStatus.IMPORT_COMPLETED_WITH_ERRORS, # 结果性状态
    ]
    # CHANGE: [2025-05-28] 将 IMPORT_QUEUED 添加到受保护的早期状态列表
    _EARLY_ACTIVE_STATUSES = [
        ImportSessionStatus.SELECT,
        ImportSessionStatus.UPLOAD,
        ImportSessionStatus.ANALYSIS_START,
        ImportSessionStatus.ANALYSIS_IN_PROGRESS,
        ImportSessionStatus.ANALYSIS_COMPLETE,
        # CHANGE: [2025-06-01] 更新早期活动状态列表
        ImportSessionStatus.CONFLICT_RESOLUTION_STARTED,
        ImportSessionStatus.CONFLICT_RESOLUTION_IN_PROGRESS,
        ImportSessionStatus.CONFLICT_RESOLUTION_PENDING,
        ImportSessionStatus.CONFLICT_RESOLUTION_COMPLETED, # 虽然是完成，但导入流程还未开始
        # END CHANGE
        # REMOVE: [2025-06-01] 移除旧的 CONFLICT_RESOLUTION
        # ImportSessionStatus.CONFLICT_RESOLUTION, 
        ImportSessionStatus.IMPORT_QUEUED,
        ImportSessionStatus.IMPORT_START,
        ImportSessionStatus.IMPORT_IN_PROGRESS,
    ]

    # CHANGE: [2025-05-29] Increase max_length to accommodate longer status names
    old_status = models.CharField(max_length=50, blank=True, null=True) 
    new_status = models.CharField(max_length=50, blank=True, null=True)
    # END CHANGE
    details = models.JSONField(blank=True, null=True)

    # CHANGE: [2025-06-03] Add _VALID_TRANSITIONS attribute
    _VALID_TRANSITIONS = {
        # 严格的单向紧邻转换
        ImportSessionStatus.SELECT: [
            ImportSessionStatus.UPLOAD,
        ],
        
        ImportSessionStatus.UPLOAD: [
            ImportSessionStatus.ANALYSIS_START,  # 唯一的下一步
            ImportSessionStatus.ERROR            # 文件上传错误
        ],
        
        ImportSessionStatus.ANALYSIS_START: [
            ImportSessionStatus.ANALYSIS_IN_PROGRESS,  # 唯一的下一步
            ImportSessionStatus.ERROR                  # 分析启动失败
        ],
        
        ImportSessionStatus.ANALYSIS_IN_PROGRESS: [
            ImportSessionStatus.ANALYSIS_COMPLETE,     # 唯一的下一步
            ImportSessionStatus.CANCELLED,             # 用户可以在分析阶段取消
            ImportSessionStatus.ERROR                  # 分析过程错误
        ],
        
        ImportSessionStatus.ANALYSIS_COMPLETE: [
            ImportSessionStatus.CONFLICT_RESOLUTION_STARTED,  # 有冲突时
        ],
        
        ImportSessionStatus.CONFLICT_RESOLUTION_STARTED: [
            ImportSessionStatus.CONFLICT_RESOLUTION_IN_PROGRESS,  # 用户进行处理
            ImportSessionStatus.CANCELLED                         # 用户可以取消
        ],
        
        ImportSessionStatus.CONFLICT_RESOLUTION_IN_PROGRESS: [
            ImportSessionStatus.CONFLICT_RESOLUTION_COMPLETED,  # 处理完成
            ImportSessionStatus.CONFLICT_RESOLUTION_PENDING,    # 搁置处理
            ImportSessionStatus.CANCELLED                       # 用户取消
        ],
        
        ImportSessionStatus.CONFLICT_RESOLUTION_PENDING: [
            ImportSessionStatus.CONFLICT_RESOLUTION_IN_PROGRESS,  # 恢复处理
            ImportSessionStatus.CANCELLED                         # 用户取消
        ],
        
        ImportSessionStatus.CONFLICT_RESOLUTION_COMPLETED: [
            ImportSessionStatus.IMPORT_QUEUED  # 唯一的下一步
        ],
        
        ImportSessionStatus.IMPORT_QUEUED: [
            ImportSessionStatus.IMPORT_START,  # 唯一的下一步
            ImportSessionStatus.ERROR          # 队列处理错误
        ],
        
        ImportSessionStatus.IMPORT_START: [
            ImportSessionStatus.IMPORT_IN_PROGRESS,  # 唯一的下一步
            ImportSessionStatus.ERROR                # 导入启动错误
        ],
        
        ImportSessionStatus.IMPORT_IN_PROGRESS: [
            # 这是唯一允许的三个结束状态，严格紧邻
            ImportSessionStatus.IMPORT_COMPLETED_SUCCESSFULLY,
            ImportSessionStatus.IMPORT_COMPLETED_WITH_ERRORS, 
            ImportSessionStatus.ERROR  # 只有真正的系统错误
        ],
        
        # 结果状态只能最终化
        ImportSessionStatus.IMPORT_COMPLETED_SUCCESSFULLY: [ImportSessionStatus.FINALIZED],
        ImportSessionStatus.IMPORT_COMPLETED_WITH_ERRORS: [ImportSessionStatus.FINALIZED],
        ImportSessionStatus.ERROR: [ImportSessionStatus.FINALIZED],
        ImportSessionStatus.CANCELLED: [ImportSessionStatus.FINALIZED],
        
        # 最终状态
        ImportSessionStatus.FINALIZED: []  # 终态，无后续转换
    }

    class Meta:
        verbose_name = "导入会话"
        verbose_name_plural = "导入会话"
        indexes = [
            models.Index(fields=["status"]),
            models.Index(fields=["created_by"]),
            models.Index(fields=["processing_user"]),
            models.Index(fields=["created_at"]),
            models.Index(fields=["last_heartbeat_at"]),
            models.Index(fields=["expires_at"]),
            models.Index(fields=["results_display_expires_at"]),
        ]

    def __str__(self):
        return f"导入会话 {self.session_id} - {self.get_status_display()}"

    def save(self, *args, **kwargs):
        is_new_instance = self._state.adding
        old_status = None
        if not is_new_instance:
            try:
                old_instance = self.__class__.objects.get(pk=self.pk)
                old_status = old_instance.status
            except self.__class__.DoesNotExist:
                is_new_instance = True # Should not happen if not adding, but as a safeguard
            except Exception as e:
                logger.error(f"ImportSession {self.pk}: Error fetching old instance in save method: {e}", exc_info=True)
                # Proceed without old_status validation if fetch fails, relying on other checks

        if not is_new_instance and old_status:
            new_status = self.status
            if old_status != new_status: # Only validate if status is actually changing
                logger.debug(f"ImportSession {self.pk}: Status change attempt from '{old_status}' to '{new_status}'.")
                # 1. 防止从结论性状态回退到早期活动状态 (现有逻辑)
                if old_status in self._CONCLUSIVE_STATUSES and new_status in self._EARLY_ACTIVE_STATUSES:
                    logger.warning(
                        f"ImportSession {self.pk}: Prevented status rollback from conclusive status "
                        f"'{old_status}' to early active status '{new_status}'. Restoring to '{old_status}'."
                    )
                    self.status = old_status
                # 2. 更严格的单向流转校验 (新增逻辑)
                # 仅当状态真的要被改变，并且不是上述回退情况时，再检查单向流转
                elif self.status != old_status: # self.status可能已被上一条规则恢复，所以重新比较
                    allowed_next_states = self._VALID_TRANSITIONS.get(old_status, [])
                    # 任何状态都可以被取消 (CANCELLED) 或直接最终化 (FINALIZED)，或在流程中出错 (ERROR)
                    # 这些是通用的"跳出"路径，不应受严格的线性流转限制
                    universal_exit_states = [ImportSessionStatus.CANCELLED, ImportSessionStatus.FINALIZED, ImportSessionStatus.ERROR]
                    
                    if new_status not in allowed_next_states and new_status not in universal_exit_states:
                        logger.warning(
                            f"ImportSession {self.pk}: Invalid state transition from '{old_status}' to '{new_status}'. "
                            f"Allowed next states are {allowed_next_states} or universal exits {universal_exit_states}. Restoring to '{old_status}'."
                        )
                        self.status = old_status

        super().save(*args, **kwargs)

    def is_active(self):
        """检查会话是否活跃（未完成、未取消且未过期）"""
        # CHANGE: [2024-07-29] 更新is_active逻辑以匹配新的状态模型和展示期概念
        if self.status in [
            ImportSessionStatus.FINALIZED,
            ImportSessionStatus.CANCELLED, # 作为短暂过渡，一旦设置，应尽快转FINALIZED，故非活跃
        ]:
            return False

        # 对于已完成或流程性错误状态，根据其特定的展示期判断是否活跃
        if self.status == ImportSessionStatus.IMPORT_COMPLETED_SUCCESSFULLY or \
           self.status == ImportSessionStatus.IMPORT_COMPLETED_WITH_ERRORS:
            if self.results_display_expires_at and timezone.now() > self.results_display_expires_at:
                return False # 结果展示期已过
            # 如果在展示期内，则认为是活跃的 (下面的通用expires_at不应再使其失效)
            return True 

        if self.status == ImportSessionStatus.ERROR: # 特指无ImportLog的流程性错误
            error_display_duration_minutes = getattr(settings, 'ERROR_SESSION_DISPLAY_MINUTES', 15) # 假设默认15分钟
            # 确保 self.updated_at 不是 None
            if self.updated_at and (timezone.now() > (self.updated_at + timedelta(minutes=error_display_duration_minutes))):
                return False # 错误信息展示期已过
            # 如果在展示期内，则认为是活跃的
            return True

        # 对于其他中间状态，检查通用会话过期时间
        if self.expires_at and timezone.now() > self.expires_at:
            return False
        
        # 如果是其他未明确列出的中间状态，且未达到通用过期，则视为活跃
        # REMOVE: [2024-07-29] 移除旧的 IMPORT_COMPLETE 和 results_display_expires_at 的直接检查
        # if self.status in [
        #     ImportSessionStatus.IMPORT_COMPLETE,
        #     ImportSessionStatus.CANCELLED,
        #     ImportSessionStatus.ERROR,
        # ]:
        #     return False
        # 
        # if self.expires_at and timezone.now() > self.expires_at:
        #     return False
        # 
        # if self.results_display_expires_at and timezone.now() > self.results_display_expires_at:
        #     return False

        return True # 所有其他情况（主要是活跃的中间状态且未过期）

    def can_be_taken_over(self, current_user: Optional[User] = None) -> bool:
        """
        检查会话是否可被接管（即，独占处理权是否可以被解除或转移）。
        主要适用场景：
        1. 会话处于 ANALYSIS_COMPLETE 状态，此时没有处理者，允许第一个用户接管以进入 CONFLICT_RESOLUTION。
        2. 会话处于 CONFLICT_RESOLUTION 状态，当前有处理者，此时基于心跳判断原处理者是否失联。
        其他"共享"阶段，不适用"接管"单一处理者的概念。
        """
        if not self.is_active(): # is_active 检查过期和终态 (CANCELLED, ERROR, FINALIZED等)
            return False

        if current_user and self.processing_user == current_user:
            # 用户不能接管自己正在处理的会话
            return False

        # 场景1: 会话分析完成，等待用户首次进入冲突处理
        # 此时，会话状态为 ANALYSIS_COMPLETE，且应该没有 processing_user。
        if self.status == ImportSessionStatus.ANALYSIS_COMPLETE:
            if not self.processing_user:
                return True # 允许任何有权限的用户接管并成为处理者
            else:
                logger.info(f"Session {self.session_id} is ANALYSIS_COMPLETE but unexpectedly has processing_user. Takeover depends on processing_user status.")
                # 继续到下面的逻辑块处理 self.processing_user 的情况

        # 场景2: 会话正在冲突处理中 (独占阶段)，已有处理者
        # CHANGE: [2025-06-01] 修改此处状态检查以匹配新的状态模型。
        # 旧状态是 CONFLICT_RESOLUTION，新状态是 CONFLICT_RESOLUTION_IN_PROGRESS
        if self.status == ImportSessionStatus.CONFLICT_RESOLUTION_IN_PROGRESS and self.processing_user:
        # END CHANGE
            if self.last_heartbeat_at:
                heartbeat_timeout_minutes = getattr(settings, "SESSION_HEARTBEAT_TIMEOUT_MINUTES", 2) 
                heartbeat_inactive_threshold = timezone.now() - datetime.timedelta(minutes=heartbeat_timeout_minutes)
                if self.last_heartbeat_at < heartbeat_inactive_threshold:
                    logger.info(f"Session {self.session_id} in {self.status} can be taken over due to heartbeat timeout.")
                    return True # 心跳超时，可接管
                else:
                    logger.info(f"Session {self.session_id} in {self.status} not takeable, heartbeat is current.")
                    return False # 心跳未超时，不可接管
            else:
                logger.warning(f"Session {self.session_id} in {self.status} has processing_user but no last_heartbeat_at. Considered not takeable immediately.")
                return False 
        
        # REMOVE: [2025-05-30] 移除了基于 SESSION_ACTIVITY_TIMEOUT_MINUTES 的旧的、被注释掉的活动超时判断逻辑。
        # 该逻辑已被心跳机制和更明确的会话过期规则取代。

        # 默认：在其他所有不明确符合接管条件的场景下，均不可接管。
        # logger.debug(f"Session {self.session_id} (status: {self.status}, processing_user: {self.processing_user}) does not meet takeover conditions.")
        return False

    def to_dict(self) -> Dict:
        """转换为字典表示，用于API响应"""
        result = {
            "session_id": str(self.session_id),
            "status": self.status,
            "file_name": self.file_name,
            "record_count": self.record_count,
            "conflict_count": self.conflict_count,
            "progress": self.progress,
            "created_at": self.created_at.isoformat() if self.created_at else None,
            "updated_at": self.updated_at.isoformat() if self.updated_at else None,
            "expires_at": self.expires_at.isoformat() if self.expires_at else None,
            "created_by": (
                {
                    "id": self.created_by.id,
                    "username": self.created_by.username,
                }
                if self.created_by
                else None
            ),
            "results_display_expires_at": self.results_display_expires_at.isoformat() if self.results_display_expires_at else None,
            "import_log_id": str(self.import_log.id) if self.import_log else None,
        }

        if self.processing_user:
            result["processing_user"] = {
                "id": self.processing_user.id,
                "username": self.processing_user.username,
            }
            result["last_activity"] = (
                self.last_activity.isoformat() if self.last_activity else None
            )

        if self.error_message:
            result["error_message"] = self.error_message

        # CHANGE: [2025-05-28] 确保 IMPORT_COMPLETED_WITH_ERRORS 也能获取 import_results_summary
        if self.status in [ImportSessionStatus.IMPORT_COMPLETED_SUCCESSFULLY, ImportSessionStatus.IMPORT_COMPLETED_WITH_ERRORS] and self.import_log:
            log = self.import_log
            result["import_results_summary"] = {
                "import_log_id": str(log.id),
                "batch_number": log.batch_number,
                "status": log.status,
                "analysis_total_rows_read": log.analysis_total_rows_read,
                "analysis_failed_rows": log.analysis_failed_rows,
                "analysis_skipped_identical": log.analysis_skipped_identical,
                "analysis_found_new_count": log.analysis_found_new_count,
                "analysis_found_update_count": log.analysis_found_update_count,
                "analysis_successfully_parsed_rows": log.analysis_successfully_parsed_rows,
                "user_decision_skipped_update_count": log.user_decision_skipped_update_count,
                "user_decision_confirmed_update_count": log.user_decision_confirmed_update_count,
                "import_task_total_records_submitted": log.import_task_total_records_submitted,
                "import_task_created_count": log.import_task_created_count,
                "import_task_updated_count": log.import_task_updated_count,
                "import_task_unchanged_count": log.import_task_unchanged_count,
                "import_task_processed_successfully_count": log.import_task_processed_successfully_count,
                "import_task_failed_count": log.import_task_failed_count,
                "overall_total_initial_records": log.overall_total_initial_records,
                "overall_user_decision_skipped_updates": log.overall_user_decision_skipped_updates,
                "overall_final_created_count": log.overall_final_created_count,
                "overall_final_updated_count": log.overall_final_updated_count,
                "overall_skipped_by_system_total": log.overall_skipped_by_system_total,
                "overall_skipped_total": log.overall_skipped_total,
                "overall_processed_successfully_total": log.overall_processed_successfully_total,
                "overall_failed_total": log.overall_failed_total,
                "error_message": log.error_log,
                "detailed_report": log.detailed_report,
            }

        return result

    def extend_session_expiry(self, minutes: int = 30): # Default from SESSION_EXPIRATION_MINUTES
        self.expires_at = timezone.now() + timedelta(minutes=minutes)
        # self.save(update_fields=['expires_at', 'updated_at']) # Let caller save


# CHANGE: [2024-07-26] 添加SessionOperation模型，用于记录会话操作日志
class SessionOperation(models.Model):
    """会话操作日志模型"""

    OPERATION_TYPES = [
        ("create_session", "创建会话"),
        ("analyze_start", "分析开始"),
        ("analyze_complete", "分析完成"),
        ("error_in_analysis", "分析出错"), # General error during analysis phase by analyzer
        ("error_in_analysis_manager", "分析管理器出错"), # Error in manager part of analysis
        ("error_in_background_analysis", "后台分析出错"), # Error in background thread
        ("import_start", "导入开始"),
        ("import_processing", "导入处理中"),
        ("import_complete", "导入完成"),
        ("import_error", "导入出错"),
        ("cancel_cleanup", "取消并清理"),
        ("takeover", "接管会话"),
        ("status_change", "状态变更"),
        ("system_cleanup_expired", "系统清理过期"),
        ("system_auto_status_update", "系统自动状态更新"),
        ("system_finalize_cancelled_session", "系统最终处理已取消会话"),
        ("system_timeout_to_error", "系统超时转错误"),
        ("system_finalize_completed_session", "系统最终处理已完成会话"),
        ("system_finalize_error_session", "系统最终处理错误会话"),
        ("system_resource_cleanup", "系统资源清理"),
        ("user_acknowledge_and_finalize_session", "用户确认会话至最终处理"),
        ("cancel_session", "取消会话"),
        ("import_queued_async", "异步导入排队"),
        ("import_start_async", "异步导入开始"),
        ("async_import_finalized", "异步导入完成"),
        ("async_import_failed", "异步导入失败"),
        ("error_in_confirm_trigger", "确认触发错误"),
        ("error", "通用错误") # Catch-all for other errors
    ]

    session = models.ForeignKey(
        ImportSession, on_delete=models.CASCADE, related_name="operations"
    )
    operation_type = models.CharField(max_length=50, choices=OPERATION_TYPES)
    user = models.ForeignKey(
        settings.AUTH_USER_MODEL, on_delete=models.CASCADE, null=True, blank=True
    )
    timestamp = models.DateTimeField(auto_now_add=True)
    # CHANGE: [2025-05-29] Increase max_length to accommodate longer status names
    old_status = models.CharField(max_length=50, blank=True, null=True) 
    new_status = models.CharField(max_length=50, blank=True, null=True)
    # END CHANGE
    details = models.JSONField(blank=True, null=True)

    class Meta:
        verbose_name = "会话操作日志"
        verbose_name_plural = "会话操作日志"
        ordering = ["-timestamp"]
        indexes = [
            models.Index(fields=["session"]),
            models.Index(fields=["operation_type"]),
            models.Index(fields=["user"]),
            models.Index(fields=["timestamp"]),
        ]

    def __str__(self):
        return f"{self.get_operation_type_display()} - {self.timestamp.strftime('%Y-%m-%d %H:%M:%S')}"

class ArchiveRecord(models.Model):
    """
    档案记录模型

    该模型包含以下主要部分：
    1. 基础标识信息 - 各种编号和标识
    2. 项目与委托信息 - 工程信息和委托方信息
    3. 试验与结果信息 - 试验过程和检测结果
    4. 档案生命周期信息 - 入库、出库、归档等流程记录
    5. 报告管理信息 - 报告发放和领取记录
    6. 样品信息 - 样品相关数据
    7. 财务信息 - 收费和价格相关记录
    8. 系统元数据 - 创建时间、更新时间等

    .. note::
       原系统的字段分组可在 :file:`docs/model_fields_reference.py` 中找到。

    .. seealso::
       :py:mod:`docs.model_fields_reference`
          原始字段分类和字段说明的完整参考。
    """

    ###################
    # 1. 基础标识信息 #
    ###################
    id = models.AutoField(primary_key=True)
    sample_number = models.CharField(
        max_length=2000, verbose_name="样品编号", blank=False, null=False
    )
    account_from_excel = models.CharField(
        max_length=2000, verbose_name="账号", blank=True, null=True
    )
    commission_number = models.CharField(
        max_length=2000,
        verbose_name="委托编号",
        blank=False,
        null=False,
        unique=True,
    )
    unified_number = models.CharField(
        max_length=2000,
        verbose_name="统一编号",
        blank=True,
        null=True,
        help_text="自动从委托编号复制",
    )
    report_number = models.CharField(
        max_length=2000, verbose_name="报告编号", blank=True, null=True
    )
    province_unified_number = models.CharField(
        max_length=2000, verbose_name="省统一报告编号", blank=True, null=True
    )
    station_code = models.CharField(
        max_length=2000, blank=True, null=True, verbose_name="站点编号"
    )
    organization_code = models.CharField(
        max_length=2000, blank=True, null=True, verbose_name="机构代号"
    )

    ########################
    # 2. 项目与委托信息    #
    ########################
    # 工程信息
    project_number = models.CharField(
        max_length=2000, verbose_name="工程编号", blank=True, null=True
    )
    project_name = models.CharField(
        max_length=2000, verbose_name="工程名称", blank=False, null=False
    )
    sub_project = models.CharField(
        max_length=2000, verbose_name="分项工程", blank=True, null=True
    )
    project_location = models.CharField(
        max_length=2000, verbose_name="工程部位", blank=True, null=True
    )
    project_address = models.CharField(
        max_length=2000, verbose_name="工程地址", blank=True, null=True
    )

    # 委托信息
    client_unit = models.CharField(
        max_length=2000, verbose_name="委托单位", blank=False, null=False
    )
    client_name = models.CharField(
        max_length=2000, verbose_name="委托人", blank=True, null=True
    )
    commission_datetime = models.DateTimeField(
        verbose_name="委托日期",
        null=True,  # 数据库允许为空
        blank=False,  # 表单验证不允许为空
    )

    #######################
    # 3. 试验与结果信息   #
    #######################
    # 试验过程
    test_start_datetime = models.DateTimeField(  # 改名并改为 DateTimeField
        verbose_name="试验开始日期", blank=True, null=True
    )
    test_end_datetime = models.DateTimeField(  # 改名并改为 DateTimeField
        verbose_name="试验结束日期", blank=True, null=True
    )
    test_person1 = models.CharField(
        max_length=2000, verbose_name="试验人1", blank=True, null=True
    )
    test_person2 = models.CharField(
        max_length=2000, verbose_name="试验人2", blank=True, null=True
    )
    data_entry_person = models.CharField(
        max_length=2000, verbose_name="数据录入人", blank=True, null=True
    )

    # 试验结果
    test_result = models.TextField(verbose_name="检测结果", blank=True, null=True)
    conclusion = models.TextField(verbose_name="结论", blank=True, null=True)
    test_parameters = models.TextField(verbose_name="检测参数", blank=True, null=True)
    unqualified_parameters = models.TextField(
        verbose_name="不合格参数", blank=True, null=True
    )

    #########################
    # 4. 档案生命周期信息   #
    #########################
    # 档案流转状态
    archive_status = models.CharField(
        max_length=2000, verbose_name="报告归档状态", blank=True, null=True
    )
    change_count = models.IntegerField(default=0, verbose_name="更改次数")
    current_status = models.CharField(
        max_length=2000, verbose_name="当前数据状态", blank=True, null=True
    )
    processing_status = models.CharField(
        max_length=2000, verbose_name="待处理状态", blank=True, null=True
    )

    # 档案文件信息
    source_file = models.ForeignKey(
        'archive_processing.UploadedFile',
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        verbose_name="关联的源文件",
        help_text="关联到此记录的原始上传文件（如PDF）"
    )
    archive_url = models.URLField(blank=True, null=True, verbose_name="档案URL链接")
    report_url = models.URLField(blank=True, null=True, verbose_name="报告URL链接")
    archive_file_path = models.TextField(
        blank=True, null=True, verbose_name="档案文件路径",
        help_text="档案PDF文件的本地存储路径"
    )
    report_file_path = models.TextField(
        blank=True, null=True, verbose_name="报告文件路径", 
        help_text="报告PDF文件的本地存储路径"
    )
    attachments_from_excel = models.TextField(
        blank=True, null=True, verbose_name="附件"
    )

    # 档案流转时间点
    storage_datetime = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name="入库日期",
        help_text="系统记录的入库日期时间，包含Excel导入的日期",
    )
    storage_person = models.CharField(
        max_length=2000, verbose_name="入库人", blank=True, null=True
    )

    outbound_datetime = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name="出库日期",
        help_text="系统记录的出库日期时间",
    )
    outbound_person = models.CharField(
        max_length=2000, blank=True, null=True, verbose_name="出库人"
    )

    archive_datetime = models.DateTimeField(
        blank=True,
        null=True,
        verbose_name="归档日期",
        help_text="系统记录的归档日期时间",
    )
    archive_person = models.CharField(
        max_length=2000, blank=True, null=True, verbose_name="归档人"
    )

    #######################
    # 5. 报告管理信息     #
    #######################
    # 报告状态
    report_issue_status = models.CharField(
        max_length=2000, verbose_name="报告发放状态", blank=True, null=True
    )

    # 第一次发放信息
    first_issue_copies = models.PositiveIntegerField(
        blank=True, null=True, verbose_name="第一次发放份数"
    )
    first_issue_datetime = models.DateTimeField(
        blank=True, null=True, verbose_name="第一次发放日期"
    )
    first_issue_person = models.CharField(
        max_length=2000, blank=True, null=True, verbose_name="第一次发放人"
    )
    first_receiver_name = models.CharField(
        max_length=2000, blank=True, null=True, verbose_name="第一次领取人"
    )
    first_receiver_unit = models.CharField(
        max_length=2000, blank=True, null=True, verbose_name="第一次领取单位"
    )
    first_receiver_phone = models.CharField(
        max_length=2000, blank=True, null=True, verbose_name="第一次领取人电话"
    )

    # 第二次发放信息
    second_issue_copies = models.PositiveIntegerField(
        blank=True, null=True, verbose_name="第二次发放份数"
    )
    second_issue_datetime = models.DateTimeField(
        blank=True, null=True, verbose_name="第二次发放日期"
    )
    second_issue_person = models.CharField(
        max_length=2000, blank=True, null=True, verbose_name="第二次发放人"
    )
    second_receiver_name = models.CharField(
        max_length=2000, blank=True, null=True, verbose_name="第二次领取人"
    )
    second_receiver_unit = models.CharField(
        max_length=2000, blank=True, null=True, verbose_name="第二次领取单位"
    )
    second_receiver_phone = models.CharField(
        max_length=2000, blank=True, null=True, verbose_name="第二次领取人电话"
    )

    # 总发放份数
    total_issue_copies = models.PositiveIntegerField(
        default=3, verbose_name="总发放份数（不含补发）"
    )

    ###################
    # 6. 样品信息     #
    ###################
    group_number = models.CharField(
        max_length=2000, verbose_name="组号", blank=True, null=True
    )
    sample_name = models.CharField(
        max_length=2000, verbose_name="样品/项目名称", blank=True, null=True
    )
    assigned_person = models.CharField(
        max_length=2000, verbose_name="分配人", blank=True, null=True
    )

    component_count = models.IntegerField(
        verbose_name="构件(桩)数", blank=True, null=True
    )
    test_point_count = models.IntegerField(verbose_name="测点数", blank=True, null=True)
    unqualified_point_count = models.IntegerField(
        verbose_name="不合格点数", blank=True, null=True
    )

    sample_retention_datetime = models.DateTimeField(
        blank=True, null=True, verbose_name="样品留样时间"
    )
    sample_remaining_time = models.IntegerField(
        blank=True, null=True, verbose_name="样品剩余时间(天)"
    )

    ###################
    # 7. 财务信息     #
    ###################
    payment_status = models.CharField(
        max_length=2000, verbose_name="收费状态", blank=True, null=True
    )
    price_adjustment_status = models.CharField(
        max_length=2000, verbose_name="价格调整状态", blank=True, null=True
    )
    standard_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name="标准价格费用",
    )
    discount_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name="折扣价格费用",
    )
    actual_price = models.DecimalField(
        max_digits=10,
        decimal_places=2,
        blank=True,
        null=True,
        verbose_name="实际价格费用",
    )

    #####################
    # 8. 系统元数据     #
    #####################
    # 导入信息
    import_user = models.ForeignKey(
        "auth.User",
        on_delete=models.SET_NULL,
        null=True,
        related_name="imported_records",
        verbose_name="导入人",
    )
    import_date = models.DateTimeField(auto_now_add=True, verbose_name="导入时间")
    batch_number = models.CharField(
        max_length=2000, blank=True, null=True, verbose_name="导入批次号"
    )
    source_system = models.CharField(
        max_length=2000, default="excel_import", verbose_name="数据来源系统"
    )

    # 系统时间戳
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    class Meta:
        verbose_name = "档案记录"
        verbose_name_plural = "档案记录"
        indexes = [
            models.Index(fields=["sample_number"]),
            models.Index(fields=["report_number"]),
            models.Index(fields=["batch_number"]),
            models.Index(fields=["commission_number"]),
        ]
        ordering = ["id"]

    def __str__(self):
        return str(self.commission_number)

    def save(self, *args, **kwargs):
        # 如果统一编号为空，则从委托编号复制
        if not self.unified_number and self.commission_number:
            self.unified_number = self.commission_number
        super().save(*args, **kwargs)

    def get_first_issue_record(self):
        """获取第一次发放记录"""
        return (
            self.issue_records.filter(
                issue_type="first", is_active=True, is_deleted=False
            )
            .order_by("-issue_date")
            .first()
        )

    def get_second_issue_record(self):
        """获取第二次发放记录"""
        return (
            self.issue_records.filter(
                issue_type="second", is_active=True, is_deleted=False
            )
            .order_by("-issue_date")
            .first()
        )

    def get_all_issue_records(self):
        """获取所有活跃的发放记录"""
        return self.issue_records.filter(is_active=True, is_deleted=False).order_by(
            "issue_type", "-issue_date"
        )  # 需要改为 issue_sequence_type


# 暂时弃用，改为report_issuing/models.py中的IssueRecord模型
# # 报告发放记录（一对多关系）
# class IssueRecord(models.Model):
#     archive_record = models.ForeignKey(
#         ArchiveRecord, on_delete=models.CASCADE, related_name="issues"
#     )
#     issuer = models.ForeignKey(
#         "auth.User", on_delete=models.SET_NULL, null=True, verbose_name="发放人"
#     )
#     issue_date = models.DateTimeField(verbose_name="发放时间")
#     receiver = models.CharField(max_length=100, verbose_name="领取人")
#
#     class Meta:
#         verbose_name = "报告发放记录"
#         verbose_name_plural = "报告发放记录"


class ImportLog(models.Model):
    IMPORT_STATUS_CHOICES = [
        ("pending", "待处理"),
        ("processing", "处理中"),
        ("completed", "完成"),
        ("failed", "失败"),
        ("partial", "部分成功"),
    ]

    # REMOVE: [2025-06-05] 移除冗余的 import_session_id_fk 字段，使用 ImportSession.import_log OneToOneField 的反向关联
    # import_session_id_fk = models.ForeignKey(
    #     ImportSession, 
    #     on_delete=models.SET_NULL,
    #     null=True, 
    #     blank=True, 
    #     related_name="import_logs_via_fk",
    #     verbose_name="关联导入会话 (FK)",
    #     help_text="通过外键关联回导入会话，补充ImportSession中OneToOneField的关联"
    # )

    batch_number = models.CharField(
        max_length=100, unique=True, verbose_name="导入批次号"
    )
    file_name = models.CharField(max_length=255, verbose_name="导入文件名")
    file_size = models.IntegerField(verbose_name="文件大小(字节)")
    file_hash = models.CharField(max_length=64, verbose_name="文件哈希值")

    # CHANGE: [2025-06-05] 修改创建人字段,从ImportSession获取
    created_by = models.ForeignKey(
        "auth.User", 
        on_delete=models.SET_NULL, 
        null=True, 
        blank=True,
        related_name="created_import_logs",
        verbose_name="创建人",
        help_text="创建此导入任务的用户,从关联的ImportSession获取"
    )
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")
    completed_at = models.DateTimeField(null=True, blank=True, verbose_name="完成时间")
    session_id = models.CharField(
        max_length=255, 
        null=True, 
        blank=True, 
        db_index=True,
        verbose_name="会话ID",
        help_text="关联的会话ID字符串，用于追溯"
    )

    import_user = models.ForeignKey(
        "auth.User", 
        on_delete=models.SET_NULL, 
        null=True, 
        verbose_name="导入操作人",
        related_name="executed_import_logs",
        help_text="实际执行导入操作的用户"
    )
    # REMOVE: [2025-06-05] 移除重复的import_date字段，使用created_at字段代替
    # import_date = models.DateTimeField(auto_now_add=True, verbose_name="导入时间")
    
    # 添加created_at字段，如果不存在的话
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")

    status = models.CharField(
        max_length=20,
        choices=IMPORT_STATUS_CHOICES,
        default="pending",
        verbose_name="导入状态",
    )
    
    # 分析阶段统计
    analysis_total_rows_read = models.IntegerField(default=0, verbose_name="分析阶段-读取Excel有效行数")
    analysis_failed_rows = models.IntegerField(default=0, verbose_name="分析阶段-识别错误的行数")
    analysis_skipped_identical = models.IntegerField(default=0, verbose_name="分析阶段-识别为内容相同的记录数")
    analysis_found_new_count = models.IntegerField(default=0, verbose_name="分析阶段-识别为新记录数")
    analysis_found_update_count = models.IntegerField(default=0, verbose_name="分析阶段-识别可更新记录数")
    analysis_successfully_parsed_rows = models.IntegerField(default=0, verbose_name="分析阶段-成功解析的行数")
    
    # 用户决策阶段统计
    user_decision_skipped_update_count = models.IntegerField(default=0, verbose_name="用户决策-跳过更新记录数")
    user_decision_confirmed_update_count = models.IntegerField(default=0, verbose_name="用户决策-确认更新记录数")

    # 导入Task (数据库执行) 阶段统计
    import_task_total_records_submitted = models.IntegerField(default=0, verbose_name="导入Task-提交处理总记录数")
    import_task_created_count = models.IntegerField(default=0, verbose_name="导入Task-成功创建记录数")
    import_task_updated_count = models.IntegerField(default=0, verbose_name="导入Task-成功更新记录数")
    import_task_unchanged_count = models.IntegerField(default=0, verbose_name="导入Task-执行时发现与DB内容相同数")
    import_task_processed_successfully_count = models.IntegerField(default=0, verbose_name="导入Task-处理成功总计数 (创建+更新+内容相同)")
    import_task_failed_count = models.IntegerField(default=0, verbose_name="导入Task-处理失败记录数")

    # Overall - 直接镜像用户要求的核心统计 (为了查询/报告方便)
    overall_total_initial_records = models.IntegerField(default=0, verbose_name="总览-最初Excel有效总行数")
    overall_user_decision_skipped_updates = models.IntegerField(default=0, verbose_name="总览-用户决策跳过更新数")
    overall_final_created_count = models.IntegerField(default=0, verbose_name="总览-最终实际创建记录数")
    overall_final_updated_count = models.IntegerField(default=0, verbose_name="总览-最终实际更新记录数")
    
    # Overall - 计算型汇总统计 (核心四个)
    overall_skipped_by_system_total = models.IntegerField(default=0, verbose_name="总计-系统跳过总数 (分析Identical + Task Unchanged)")
    overall_skipped_total = models.IntegerField(default=0, verbose_name="总计-全部跳过总数 (系统总跳过 + 用户决策跳过)")
    overall_processed_successfully_total = models.IntegerField(default=0, verbose_name="总计-最终成功处理记录数 (导入Task成功处理数 + 用户决策跳过数 + 分析阶段Identical数)")
    overall_failed_total = models.IntegerField(default=0, verbose_name="总计-最终失败记录数 (分析阶段失败数 + 导入Task阶段失败数)")

    error_log = models.TextField(blank=True, verbose_name="错误日志")
    processing_time = models.FloatField(default=0, verbose_name="处理时间(秒)")
    detailed_report = models.JSONField(
        default=dict,
        help_text="详细的导入报告，包含创建、更新、跳过和错误记录的信息",
        verbose_name="详细报告",
    )

    class Meta:
        verbose_name = "导入日志"
        verbose_name_plural = "导入日志"
        ordering = ["-created_at"]

    def __str__(self):
        return f"{self.batch_number} ({self.file_name})"


class ChangeLogBatch(models.Model):
    """变更批次，记录一组相关变更的上下文信息"""

    batch_id = models.UUIDField(default=uuid.uuid4, unique=True)
    import_log = models.ForeignKey(
        ImportLog, on_delete=models.CASCADE, null=True, related_name="change_batches"
    )
    change_source = models.CharField(
        max_length=50,
        choices=[
            ("excel_import", "Excel导入"),
            ("manual_edit", "手动编辑"),
            ("api_update", "API更新"),
            ("system", "系统操作"),
            ("batch_operation", "批量操作"),
            ("manual_rollback", "手动回滚"),
        ],
        default="excel_import",
        verbose_name="变更来源",
    )
    change_reason = models.TextField(blank=True, verbose_name="变更原因")
    changed_at = models.DateTimeField(auto_now_add=True, verbose_name="变更时间")
    changed_by = models.ForeignKey(
        "auth.User", on_delete=models.SET_NULL, null=True, verbose_name="变更用户"
    )

    # 批次级别的元数据
    affected_records_count = models.IntegerField(default=0, verbose_name="受影响记录数")
    summary = models.JSONField(
        default=dict, help_text="变更摘要统计", verbose_name="变更摘要"
    )

    # 增加客户端信息
    client_ip = models.GenericIPAddressField(
        null=True, blank=True, verbose_name="客户端IP"
    )
    user_agent = models.TextField(null=True, blank=True, verbose_name="用户代理")

    class Meta:
        verbose_name = "变更批次"
        verbose_name_plural = "变更批次"
        ordering = ["-changed_at"]
        # 增加索引提高查询性能
        indexes = [
            models.Index(fields=["changed_at"]),
            models.Index(fields=["change_source"]),
            models.Index(fields=["changed_by"]),
        ]

    def __str__(self):
        return f"批次 {self.batch_id} ({self.get_change_source_display()})"

    def get_record_changes_count(self):
        """获取记录变更数量"""
        return self.record_changes.count()

    def get_affected_records(self):
        """获取影响的记录列表"""
        return ArchiveRecord.objects.filter(change_logs__batch=self).distinct()

    def get_critical_changes_count(self):
        """获取关键字段变更数量"""
        return FieldChangeLog.objects.filter(
            record_change__batch=self, field_importance="critical"
        ).count()

    def get_stats(self):
        """获取批次统计信息"""
        stats = {
            "total_records": self.record_changes.count(),
            "created_records": self.record_changes.filter(change_type="create").count(),
            "updated_records": self.record_changes.filter(change_type="update").count(),
            "deleted_records": self.record_changes.filter(change_type="delete").count(),
            "rollback_records": self.record_changes.filter(is_rollback=True).count(),
            "field_changes": FieldChangeLog.objects.filter(
                record_change__batch=self
            ).count(),
            "critical_changes": FieldChangeLog.objects.filter(
                record_change__batch=self, field_importance="critical"
            ).count(),
        }
        return stats

    def update_summary(self):
        """更新变更摘要"""
        self.summary = self.get_stats()
        self.affected_records_count = (
            self.record_changes.values("record").distinct().count()
        )
        self.save(update_fields=["summary", "affected_records_count"])


class RecordChangeLog(models.Model):
    """记录级变更日志，记录单条档案记录的整体变更"""

    batch = models.ForeignKey(
        ChangeLogBatch, on_delete=models.CASCADE, related_name="record_changes"
    )
    record = models.ForeignKey(
        ArchiveRecord, on_delete=models.CASCADE, related_name="change_logs"
    )

    # 添加版本号字段
    version_number = models.PositiveIntegerField(verbose_name="版本号")

    change_type = models.CharField(
        max_length=20,
        choices=[
            ("create", "新建"),
            ("update", "更新"),
            ("delete", "删除"),
            ("rollback", "回滚"),
        ],
        verbose_name="变更类型",
    )

    # 添加回滚相关字段
    is_rollback = models.BooleanField(default=False, verbose_name="是否为回滚操作")
    rollback_source_version = models.PositiveIntegerField(
        null=True, blank=True, verbose_name="回滚源版本号"
    )

    # 记录变更前后的完整状态
    record_before = models.JSONField(null=True, blank=True, verbose_name="变更前状态")
    record_after = models.JSONField(null=True, blank=True, verbose_name="变更后状态")

    # 变更摘要
    changed_fields_count = models.IntegerField(default=0, verbose_name="变更字段数")

    # 已有的时间和用户字段
    changed_by = models.ForeignKey(
        "auth.User", on_delete=models.SET_NULL, null=True, verbose_name="变更人"
    )
    changed_at = models.DateTimeField(auto_now_add=True, verbose_name="变更时间")

    # 增加变更备注
    notes = models.TextField(blank=True, null=True, verbose_name="变更备注")

    class Meta:
        verbose_name = "记录变更日志"
        verbose_name_plural = "记录变更日志"
        unique_together = [("record", "version_number")]
        ordering = ["-version_number"]
        # 增加组合索引提高查询性能
        indexes = [
            models.Index(fields=["record", "version_number"]),
            models.Index(fields=["changed_at"]),
            models.Index(fields=["change_type"]),
        ]

    def __str__(self):
        if self.is_rollback:
            return f"{self.record} - 版本 {self.version_number} (回滚到版本 {self.rollback_source_version})"
        return f"{self.record} - 版本 {self.version_number} ({self.get_change_type_display()})"

    def get_changed_by_details(self):
        """获取变更人的详细信息"""
        if not self.changed_by:
            if self.batch and self.batch.changed_by:
                return {
                    "id": self.batch.changed_by.id,
                    "username": self.batch.changed_by.username,
                    "full_name": f"{self.batch.changed_by.first_name} {self.batch.changed_by.last_name}".strip()
                    or self.batch.changed_by.username,
                }
            return None

        return {
            "id": self.changed_by.id,
            "username": self.changed_by.username,
            "full_name": f"{self.changed_by.first_name} {self.changed_by.last_name}".strip()
            or self.changed_by.username,
        }

    def get_change_time(self):
        """获取变更时间"""
        return self.changed_at

    def get_critical_changes(self):
        """获取关键字段变更列表"""
        return self.field_changes.filter(field_importance="critical")

    def get_important_changes(self):
        """获取重要字段变更列表"""
        return self.field_changes.filter(field_importance="important")

    @property
    def has_critical_changes(self):
        """是否包含关键字段变更"""
        return self.field_changes.filter(field_importance="critical").exists()

    @property
    def has_important_changes(self):
        """是否包含重要字段变更"""
        return self.field_changes.filter(field_importance="important").exists()

    @property
    def summary_text(self):
        """获取变更摘要文本"""
        if self.change_type == "create":
            return f"创建记录 ({self.changed_fields_count} 个字段)"
        elif self.change_type == "update":
            return f"更新记录 ({self.changed_fields_count} 个字段)"
        elif self.change_type == "delete":
            return "删除记录"
        elif self.is_rollback:
            return f"回滚到版本 {self.rollback_source_version}"
        return f"变更记录 ({self.changed_fields_count} 个字段)"


class FieldChangeLog(models.Model):
    """字段级变更日志，记录单个字段的变更细节"""

    record_change = models.ForeignKey(
        RecordChangeLog, on_delete=models.CASCADE, related_name="field_changes"
    )
    field_name = models.CharField(max_length=100, verbose_name="字段名")
    field_label = models.CharField(max_length=100, verbose_name="字段中文名称")

    old_value = models.TextField(null=True, blank=True, verbose_name="旧值")
    new_value = models.TextField(null=True, blank=True, verbose_name="新值")

    # 增加字段变更动作类型
    change_action = models.CharField(
        max_length=10,
        choices=[
            ("added", "新增"),
            ("modified", "修改"),
            ("removed", "删除"),
            ("unchanged", "未变更"),
        ],
        default="modified",
        verbose_name="变更动作",
    )

    # 字段重要性分类
    field_importance = models.CharField(
        max_length=20,
        choices=[
            ("critical", "关键字段"),
            ("important", "重要字段"),
            ("normal", "普通字段"),
        ],
        default="normal",
        verbose_name="字段重要性",
    )

    class Meta:
        verbose_name = "字段变更日志"
        verbose_name_plural = "字段变更日志"
        # 增加索引以提高查询性能
        indexes = [
            models.Index(fields=["field_name"]),
            models.Index(fields=["change_action"]),
            models.Index(fields=["field_importance"]),
        ]

    def __str__(self):
        return f"{self.field_label}: {self.old_value} -> {self.new_value}"

    def get_change_description(self):
        """获取变更描述文本"""
        if self.change_action == "added":
            return f"添加了{self.field_label}: {self.new_value}"
        elif self.change_action == "modified":
            return f"将{self.field_label}从 \"{self.old_value or '空'}\" 改为 \"{self.new_value or '空'}\""
        elif self.change_action == "removed":
            return f"删除了{self.field_label}: {self.old_value}"
        else:
            return f"{self.field_label}未变更"

    def save(self, *args, **kwargs):
        # 自动判断变更动作类型
        if self.old_value is None and self.new_value is not None:
            self.change_action = "added"
        elif self.old_value is not None and self.new_value is None:
            self.change_action = "removed"
        elif self.old_value == self.new_value:
            self.change_action = "unchanged"
        else:
            self.change_action = "modified"
        super().save(*args, **kwargs)


def create_record_change_log(
    self,
    record,
    batch,
    version_number,
    change_type,
    record_before=None,
    changes=None,
    is_rollback=False,
    rollback_source_version=None,
):
    """创建记录级变更日志

    Args:
        record: 档案记录实例
        batch: 变更批次
        version_number: 版本号
        change_type: 变更类型 ('create', 'update', 'delete', 'rollback')
        record_before: 变更前的状态 (可选)
        changes: 变更字段列表 (可选)
        is_rollback: 是否为回滚操作
        rollback_source_version: 回滚源版本号

    Returns:
        RecordChangeLog: 创建的变更日志实例
    """
    # 记录变更后的完整状态
    record_after = {}
    for field in record._meta.fields:
        if not field.primary_key and field.name != "id":
            value = getattr(record, field.name)
            record_after[field.name] = str(value) if value is not None else None

    # 创建记录级变更日志，直接使用batch的created_by，保持一致性
    record_change = RecordChangeLog.objects.create(
        batch=batch,
        record=record,
        version_number=version_number,
        change_type=change_type,
        record_before=record_before,
        record_after=record_after,
        changed_fields_count=len(changes) if changes else len(record_after),
        is_rollback=is_rollback,
        rollback_source_version=rollback_source_version,
        changed_by=batch.changed_by,  # 确保与批次的变更人一致
    )

    # 创建字段变更日志
    field_logs = []

    if change_type == "create":
        # 创建操作 - 所有字段都是新增
        for field, value in record_after.items():
            field_logs.append(
                FieldChangeLog(
                    record_change=record_change,
                    field_name=field,
                    field_label=self._get_field_label(field),
                    old_value=None,
                    new_value=value,
                    field_importance=self._get_field_importance(field),
                    change_action="added",
                )
            )

    elif change_type in ["update", "rollback"]:
        # 更新或回滚操作 - 比较前后状态
        all_fields = (
            set(record_before.keys()) | set(record_after.keys())
            if record_before
            else set(record_after.keys())
        )

        for field in all_fields:
            old_value = record_before.get(field) if record_before else None
            new_value = record_after.get(field)

            # 确定变更动作
            if old_value is None and new_value is not None:
                action = "added"
            elif old_value is not None and new_value is None:
                action = "removed"
            elif old_value != new_value:
                action = "modified"
            else:
                # 如果值相同，通常不记录，但可以设置为unchanged
                continue  # 跳过相同值字段

            field_logs.append(
                FieldChangeLog(
                    record_change=record_change,
                    field_name=field,
                    field_label=self._get_field_label(field),
                    old_value=old_value,
                    new_value=new_value,
                    field_importance=self._get_field_importance(field),
                    change_action=action,
                )
            )

    # 批量创建字段变更日志
    if field_logs:
        FieldChangeLog.objects.bulk_create(field_logs)

    # 更新批次摘要
    batch.update_summary()

    return record_change


class ChangeOrder(models.Model):
    """
    档案更改单模型，用于管理对已归档档案记录的正式变更

    更改单是对已归档档案进行变更的业务凭证，包含变更原因、关联档案、变更明细等信息
    每个更改单可关联多个档案条目，但只能针对已归档状态的条目
    """

    # 基本信息
    order_number = models.CharField(
        max_length=50, unique=True, verbose_name="更改单编号"
    )
    created_by = models.ForeignKey(
        "auth.User",
        on_delete=models.PROTECT,
        related_name="created_change_orders",
        verbose_name="创建人",
    )
    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")
    updated_at = models.DateTimeField(auto_now=True, verbose_name="更新时间")

    # 状态信息
    STATUS_CHOICES = [
        ("draft", "草稿"),
        ("pending", "待处理"),
        ("processing", "处理中"),
        ("completed", "已完成"),
        ("cancelled", "已取消"),
        ("deleted", "已删除"),
    ]
    status = models.CharField(
        max_length=20, choices=STATUS_CHOICES, default="draft", verbose_name="状态"
    )
    is_deleted = models.BooleanField(default=False, verbose_name="是否删除")

    # 业务信息
    change_reason = models.TextField(verbose_name="变更原因")
    execution_time = models.DateTimeField(
        null=True, blank=True, verbose_name="实际执行时间"
    )
    executed_by = models.ForeignKey(
        "auth.User",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="executed_change_orders",
        verbose_name="执行人",
    )

    # 变更内容摘要
    records_count = models.PositiveIntegerField(default=0, verbose_name="关联记录数")
    changes_count = models.PositiveIntegerField(default=0, verbose_name="变更字段总数")
    notes = models.TextField(blank=True, null=True, verbose_name="备注")

    # 关联变更批次 - 执行变更时会创建对应的批次
    change_batch = models.OneToOneField(
        ChangeLogBatch,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="change_order",
        verbose_name="关联变更批次",
    )

    # 扫描件/附件信息
    attachment = models.FileField(
        upload_to="archives/change_orders/%Y/%m/",
        null=True,
        blank=True,
        verbose_name="附件",
    )
    attachment_name = models.CharField(
        max_length=255, null=True, blank=True, verbose_name="附件名称"
    )
    attachment_type = models.CharField(
        max_length=50, null=True, blank=True, verbose_name="附件类型"
    )

    # 回滚相关
    is_rollback_source = models.BooleanField(default=False, verbose_name="是否已被回滚")
    rollback_order = models.ForeignKey(
        "self",
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="source_orders",
        verbose_name="回滚更改单",
    )

    class Meta:
        verbose_name = "档案更改单"
        verbose_name_plural = "档案更改单"
        ordering = ["-created_at"]
        indexes = [
            models.Index(fields=["order_number"]),
            models.Index(fields=["status"]),
            models.Index(fields=["created_at"]),
        ]

    def __str__(self):
        return f"更改单 {self.order_number} ({self.get_status_display()})"

    def save(self, *args, **kwargs):
        # 首次创建时生成更改单编号
        if not self.order_number:
            # 生成规则：CO-年月日-4位序号
            from django.utils import timezone

            today = timezone.now().strftime("%Y%m%d")

            # 获取当天最大序号
            last_order = (
                ChangeOrder.objects.filter(order_number__startswith=f"CO-{today}")
                .order_by("-order_number")
                .first()
            )

            if last_order:
                # 提取序号并加1
                try:
                    last_sequence = int(last_order.order_number.split("-")[-1])
                    sequence = last_sequence + 1
                except (ValueError, IndexError):
                    sequence = 1
            else:
                sequence = 1

            self.order_number = f"CO-{today}-{sequence:04d}"

        super().save(*args, **kwargs)

    def delete(self, *args, **kwargs):
        """重写删除方法，实现软删除"""
        self.is_deleted = True
        self.status = "deleted"
        self.save()

    def hard_delete(self, *args, **kwargs):
        """真实删除方法，仅在特殊情况下使用"""
        super().delete(*args, **kwargs)

    def cancel(self, user=None):
        """取消更改单"""
        if self.status not in ["draft", "pending"]:
            raise ValueError(f"状态为 {self.get_status_display()} 的更改单不能取消")

        self.status = "cancelled"
        self.save()
        return True

    def execute(self, user):
        """执行更改单，创建变更批次并应用变更"""
        from django.utils import timezone

        if self.status != "pending":
            raise ValueError(
                f"只有待处理状态的更改单可以执行，当前状态: {self.get_status_display()}"
            )

        if not user:
            raise ValueError("执行人不能为空")

        # 执行更改单逻辑
        # TODO: 实现实际的执行逻辑

        # 更新状态
        self.status = "completed"
        self.execution_time = timezone.now()
        self.executed_by = user
        self.save()

        return True


class ChangeOrderItem(models.Model):
    """
    更改单条目，表示更改单中的每一个档案记录变更

    每个条目对应一个档案记录，包含该记录的所有字段变更
    """

    change_order = models.ForeignKey(
        ChangeOrder,
        on_delete=models.CASCADE,
        related_name="items",
        verbose_name="更改单",
    )
    archive_record = models.ForeignKey(
        ArchiveRecord,
        on_delete=models.PROTECT,
        related_name="change_order_items",
        verbose_name="档案记录",
    )

    # 变更前状态快照
    record_before = models.JSONField(verbose_name="变更前状态")

    # 变更计划
    planned_changes = models.JSONField(verbose_name="计划变更")
    changes_count = models.PositiveIntegerField(default=0, verbose_name="变更字段数")

    # 执行结果
    is_executed = models.BooleanField(default=False, verbose_name="是否已执行")
    execution_time = models.DateTimeField(
        null=True, blank=True, verbose_name="执行时间"
    )
    execution_result = models.CharField(
        max_length=20, null=True, blank=True, verbose_name="执行结果"
    )
    execution_message = models.TextField(null=True, blank=True, verbose_name="执行消息")

    # 关联的记录变更日志
    record_change_log = models.ForeignKey(
        RecordChangeLog,
        on_delete=models.SET_NULL,
        null=True,
        blank=True,
        related_name="change_order_item",
        verbose_name="记录变更日志",
    )

    class Meta:
        verbose_name = "更改单条目"
        verbose_name_plural = "更改单条目"
        unique_together = [("change_order", "archive_record")]

    def __str__(self):
        return f"条目: {self.archive_record.unified_number}"

    def get_field_changes(self):
        """获取字段变更列表"""
        changes = []
        for field_name, new_value in self.planned_changes.items():
            old_value = self.record_before.get(field_name)
            if old_value != new_value:
                # 获取字段显示名
                field_label = field_name  # 默认使用字段名
                try:
                    field = ArchiveRecord._meta.get_field(field_name)
                    field_label = field.verbose_name
                except:
                    pass

                changes.append(
                    {
                        "field_name": field_name,
                        "field_label": field_label,
                        "old_value": old_value,
                        "new_value": new_value,
                    }
                )
        return changes


class ChangeOrderAttachment(models.Model):
    """
    更改单附件模型，存储更改单相关的上传文件

    包括扫描件、审批文件等与更改单相关的附件
    """

    # 关联的更改单
    change_order = models.ForeignKey(
        ChangeOrder,
        on_delete=models.CASCADE,
        related_name="attachments",
        verbose_name="更改单",
    )

    # 附件文件
    file = models.FileField(
        upload_to="archives/change_orders/attachments/%Y/%m/", verbose_name="附件文件"
    )

    # 文件信息
    filename = models.CharField(max_length=255, verbose_name="文件名")
    file_type = models.CharField(
        max_length=50, blank=True, null=True, verbose_name="文件类型"
    )
    file_size = models.PositiveIntegerField(default=0, verbose_name="文件大小(字节)")
    description = models.TextField(blank=True, null=True, verbose_name="文件描述")

    # 上传信息
    uploaded_by = models.ForeignKey(
        "auth.User",
        on_delete=models.SET_NULL,
        null=True,
        related_name="uploaded_attachments",
        verbose_name="上传人",
    )
    upload_time = models.DateTimeField(auto_now_add=True, verbose_name="上传时间")

    class Meta:
        verbose_name = "更改单附件"
        verbose_name_plural = "更改单附件"
        ordering = ["-upload_time"]

    def __str__(self):
        return f"附件: {self.filename} - {self.change_order.order_number}"

    def save(self, *args, **kwargs):
        # 保存前处理文件名和大小
        if not self.filename and self.file:
            self.filename = os.path.basename(self.file.name)

        if self.file and not self.file_size:
            # 获取文件大小
            try:
                self.file_size = self.file.size
            except:
                pass

        # 推断文件类型
        if self.file and not self.file_type:
            ext = os.path.splitext(self.file.name)[1].lower()
            if ext in [".pdf"]:
                self.file_type = "pdf"
            elif ext in [".jpg", ".jpeg", ".png", ".gif", ".bmp"]:
                self.file_type = "image"
            elif ext in [".doc", ".docx"]:
                self.file_type = "word"
            elif ext in [".xls", ".xlsx"]:
                self.file_type = "excel"
            else:
                self.file_type = "other"

        super().save(*args, **kwargs)


# CHANGE: [2024-07-27] 添加ImportConflictDetail模型用于持久化冲突记录
class ImportConflictDetail(models.Model):
    """导入冲突详情模型，存储单条记录的冲突分析结果"""
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False, verbose_name="冲突详情ID")
    session = models.ForeignKey(
        ImportSession, 
        on_delete=models.CASCADE, 
        related_name="conflict_details",
        verbose_name="关联导入会话"
    )
    commission_number = models.CharField(max_length=2000, verbose_name="委托编号", db_index=True)
    excel_row_number = models.IntegerField(verbose_name="Excel行号")
    
    # 指向已存在的ArchiveRecord的ID，如果是新记录则为NULL
    existing_record_pk = models.IntegerField(null=True, blank=True, verbose_name="现有记录主键") 
    
    CONFLICT_TYPES = [
        ("new", "新记录"),
        ("update", "需更新"),
        ("identical", "完全相同"),
        # 可以根据需要添加其他类型，例如 "error_parsing_row"
    ]
    conflict_type = models.CharField(
        max_length=20, 
        choices=CONFLICT_TYPES,
        verbose_name="冲突类型"
    )
    
    # 用户针对此冲突的解决方案 (如果适用)
    # 例如：'skip', 'update', 'create_new' (尽管CREATE_NEW通常会转为smart_update)
    user_resolution = models.CharField(max_length=20, blank=True, null=True, verbose_name="用户解决方案")

    created_at = models.DateTimeField(auto_now_add=True, verbose_name="创建时间")

    class Meta:
        verbose_name = "导入冲突详情"
        verbose_name_plural = "导入冲突详情"
        ordering = ['session', 'excel_row_number']
        indexes = [
            models.Index(fields=['session', 'commission_number']),
            models.Index(fields=['session', 'conflict_type']),
        ]

    def __str__(self):
        session_id_short = str(self.session.session_id)[:8] if self.session else "N/A"
        return f"冲突: {self.commission_number} (会话: {session_id_short}..., 行: {self.excel_row_number}) - {self.get_conflict_type_display()}"

    # REMOVED: [2025-06-19] get_differences 和 to_dict 方法已被移除，将由DRF序列化器替代。


class ImportFieldDifference(models.Model):
    """
    导入字段差异模型

    存储单个字段在导入时发现的差异，关联到具体的冲突详情记录。
    这个模型取代了原有的 ImportConflictDetail.differences_json 字段。
    """
    id = models.UUIDField(primary_key=True, default=uuid.uuid4, editable=False, verbose_name="差异ID")
    conflict_detail = models.ForeignKey(
        ImportConflictDetail, 
        on_delete=models.CASCADE, 
        related_name='field_differences', 
        verbose_name="关联冲突详情"
    )
    field_name = models.CharField(max_length=255, verbose_name="字段名", db_index=True)
    
    # 使用JSONField以灵活存储各种类型的值
    existing_value = models.JSONField(null=True, blank=True, verbose_name="数据库现有值")
    imported_value = models.JSONField(null=True, blank=True, verbose_name="Excel导入值")

    class Meta:
        verbose_name = "导入字段差异"
        verbose_name_plural = "导入字段差异"
        ordering = ['conflict_detail', 'field_name']
        indexes = [
            models.Index(fields=['conflict_detail', 'field_name']),
        ]

    def __str__(self):
        return f"字段差异: {self.field_name} (冲突ID: {str(self.conflict_detail.id)[:8]})"
