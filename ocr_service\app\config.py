"""
OCR 微服务配置模块
"""
import os
from typing import Optional
from pydantic import BaseSettings, Field


class Settings(BaseSettings):
    """OCR 服务配置"""
    
    # 服务基础配置
    app_name: str = Field(default="OCR Service", description="应用名称")
    app_version: str = Field(default="1.0.0", description="应用版本")
    debug: bool = Field(default=False, description="调试模式")
    
    # 服务器配置
    host: str = Field(default="0.0.0.0", description="服务器主机")
    port: int = Field(default=8001, description="服务器端口")
    workers: int = Field(default=1, description="工作进程数")
    
    # OCR 配置
    ocr_version: str = Field(default="PP-OCRv4", description="PaddleOCR 版本")
    ocr_language: str = Field(default="ch", description="OCR 语言")
    use_gpu: bool = Field(default=False, description="是否使用 GPU")
    
    # 性能配置
    max_image_size: int = Field(default=10 * 1024 * 1024, description="最大图像大小 (字节)")
    request_timeout: float = Field(default=30.0, description="请求超时时间 (秒)")
    max_concurrent_requests: int = Field(default=10, description="最大并发请求数")
    
    # 日志配置
    log_level: str = Field(default="INFO", description="日志级别")
    log_format: str = Field(default="json", description="日志格式 (json/text)")
    
    # 监控配置
    enable_metrics: bool = Field(default=True, description="启用指标收集")
    metrics_port: int = Field(default=8002, description="指标端口")
    
    # 健康检查配置
    health_check_interval: float = Field(default=30.0, description="健康检查间隔 (秒)")
    
    class Config:
        env_prefix = "OCR_"
        case_sensitive = False


# 全局配置实例
settings = Settings()
