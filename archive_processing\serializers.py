# CHANGE: [2024-03-29] 将序列化器独立到 serializers.py 文件 #AFM-28
from rest_framework import serializers
from django.core.exceptions import ValidationError as DjangoValidationError
from .services import UploadService
import logging
from .models import UploadedFile, ProcessingTask

logger = logging.getLogger(__name__)

class UploadedFileSerializer(serializers.ModelSerializer):
    """UploadedFile模型的序列化器，用于在任务列表中嵌套显示。"""
    # 修正：使用SerializerMethodField以安全地处理user可能为None的情况
    username = serializers.SerializerMethodField()

    class Meta:
        model = UploadedFile
        fields = ('file_id', 'original_name', 'file_size', 'upload_time', 'username', 'archive_box_number')

    def get_username(self, obj):
        """获取用户名，如果用户不存在则返回默认值。"""
        if obj.uploader:
            return obj.uploader.username
        return "系统上传"


class ProcessingTaskSerializer(serializers.ModelSerializer):
    """ProcessingTask模型的序列化器。"""
    # 嵌套显示关联的UploadedFile信息
    file = UploadedFileSerializer(read_only=True)
    
    class Meta:
        model = ProcessingTask
        fields = (
            'task_id',
            'status',
            'progress',
            'created_at',
            'updated_at',
            'error_message',
            'file', # 使用嵌套序列化器
        )
        read_only_fields = (
            'task_id',
            'status',
            'progress',
            'created_at',
            'updated_at',
            'error_message',
        )

class PDFUploadSerializer(serializers.Serializer):
    """PDF上传请求的序列化器，用于验证输入。"""
    # CHANGE: [2024-03-29] 重命名 pdf_file 为 file 以匹配前端和通用性 #AFM-16
    file = serializers.FileField()
    # CHANGE: [2025-06-22] Rename target_box_number to archive_box_number
    archive_box_number = serializers.CharField(max_length=100, required=True, help_text="本次上传处理完成后文件最终归属的物理档案盒号")

    def validate_file(self, value):
        """验证上传的文件是否有效 (委托给 UploadService)。"""
        # Thinking: 调用 UploadService 的静态方法进行文件验证。
        # 这里的 value 是 InMemoryUploadedFile 或 TemporaryUploadedFile 对象
        try:
            UploadService.validate_file(value)
        except DjangoValidationError as e:
            logger.warning(f"文件验证失败: {e.message}")
            # 在 DRF 序列化器中，应抛出 serializers.ValidationError
            raise serializers.ValidationError(e.message)
        except Exception as e:
            logger.error(f"文件验证时发生意外错误: {str(e)}")
            raise serializers.ValidationError(f"文件验证时发生内部错误: {str(e)}") # 抛出序列化器错误
        return value

    # CHANGE: [2024-04-15] Rename validation method for the new field name
    def validate_archive_box_number(self, value):
        """验证分配的档案盒号格式 (如果需要特定格式)。"""
        # Thinking: 可以添加更具体的档案盒号格式验证逻辑。
        # 例如，检查是否以 'BOX-' 开头等。
        # 目前仅作非空检查 (由 required=True 实现)。
        if not value:
             raise serializers.ValidationError("分配的档案盒号不能为空。")
        # 简单的示例：检查是否包含非法字符
        # import re
        # if not re.match(r'^[A-Za-z0-9\-]+$', value): # Allow letters, numbers, hyphen
        #     raise serializers.ValidationError("分配的档案盒号包含无效字符。")
        return value

    # 可选: 添加 validate 方法进行跨字段验证
    # def validate(self, data):
    #     """执行跨字段验证。"""
    #     # file = data.get('file')
    #     # target_box_number = data.get('target_box_number')
    #     # if file and target_box_number:
    #     #     # 示例：文件名不能与盒号相同（无意义的例子）
    #     #     if file.name == target_box_number:
    #     #         raise serializers.ValidationError("文件名不能与目标档案盒号相同。")
    #     return data 