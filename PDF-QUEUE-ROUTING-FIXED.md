# PDF队列路由问题解决方案

## 🎉 问题已解决

PDF处理任务现在已经成功路由到专用的 `pdf_processing` 队列，并由PDF专用worker处理。

## 🔍 问题原因

### 主要问题

**任务路由配置没有生效**：虽然在 `settings.py` 中正确配置了 `CELERY_TASK_ROUTES`，但任务仍然被发送到默认的 `celery` 队列而不是 `pdf_processing` 队列。

### 根本原因

Celery的路由配置在某些情况下可能不会自动应用到 `.delay()` 方法调用。这可能是由于：

1. 配置加载时机问题
2. Django与Celery的集成问题
3. 任务发送时的路由解析问题

## 🔧 解决方案

### 1. 修改任务分发函数

将 `dispatch_pdf_processing` 函数从使用 `.delay()` 改为使用 `.apply_async()` 并显式指定队列：

```python
def dispatch_pdf_processing(task_id, use_parallel=True, chunk_size=20):
    """决策函数：根据参数选择适合的处理方式"""
    # CHANGE: [2025-07-03] 显式指定队列以确保PDF任务路由到专用队列
    if use_parallel:
        return process_pdf_parallel_task.apply_async(
            args=[task_id, chunk_size],
            queue='pdf_processing',
            routing_key='pdf_processing'
        )
    else:
        return process_pdf_serial_task.apply_async(
            args=[task_id],
            queue='pdf_processing',
            routing_key='pdf_processing'
        )
```

### 2. 保持队列配置

保持了正确的队列和交换机配置：

```python
# 定义交换机
default_exchange = Exchange('default', type='direct')

# 定义任务队列
CELERY_TASK_QUEUES = [
    Queue('default', default_exchange, routing_key='default'),
    Queue('pdf_processing', default_exchange, routing_key='pdf_processing'),
]

# 设置默认队列和交换机
CELERY_TASK_DEFAULT_QUEUE = 'default'
CELERY_TASK_DEFAULT_EXCHANGE = 'default'
CELERY_TASK_DEFAULT_EXCHANGE_TYPE = 'direct'
CELERY_TASK_DEFAULT_ROUTING_KEY = 'default'
```

### 3. Worker配置

确保worker正确监听对应的队列：

```yaml
# PDF专用worker
worker-pdf:
  command: celery -A archive_flow_manager worker --queues=pdf_processing --concurrency=2

# 默认worker  
worker-default:
  command: celery -A archive_flow_manager worker --queues=default --concurrency=4
```

## ✅ 验证结果

### Worker统计显示成功

```
📊 Worker 统计:
  pdf@f1da4da36711:
    - 进程数: 2
    - 已处理任务: {
        'process_pdf_parallel_task': 2,
        'process_pdf_three_phase_coordinator_task': 2,  
        'process_pdf_ocr_task': 1,
        'process_pdf_serial_task': 2
      }
    - 运行时间: 716

  default@d29f2f6640cc:
    - 进程数: 4
    - 已处理任务: {'archive_records.tasks.process_finalized_sessions_task': 1}
    - 运行时间: 716
```

### 任务正确分离

- ✅ **PDF任务** → `pdf_processing` 队列 → PDF专用worker
- ✅ **一般任务** → `default` 队列 → 默认worker
- ✅ **清理任务** → `default` 队列 → 默认worker

## 📊 当前架构

### 队列分工

1. **`pdf_processing` 队列**（并发数：2）
   - `process_pdf_serial_task`
   - `process_pdf_parallel_task`
   - `process_pdf_ocr_task`
   - `aggregate_pdf_ocr_results_task`
   - `process_pdf_with_ocr_results_task`
   - `process_pdf_three_phase_coordinator_task`

2. **`default` 队列**（并发数：4）
   - `archive_records.tasks.*`
   - `cleanup_*` 任务
   - 其他一般任务

### 性能优化效果

- **资源隔离**：PDF处理不会影响其他任务
- **并发控制**：PDF任务限制为2个并发，避免内存溢出
- **专用处理**：PDF worker专门优化用于处理PDF任务
- **负载均衡**：不同类型任务分离处理

## 🚀 使用方式

### 1. 启动服务

```bash
# Docker方式
docker-compose up -d

# 查看worker状态
python scripts/celery_monitor.py status
```

### 2. 监控队列

```bash
# 查看整体状态
python scripts/celery_monitor.py status

# 查看队列信息
python scripts/celery_monitor.py queues

# 动态调整并发数
python scripts/celery_monitor.py scale pdf 3
```

### 3. 手动触发任务

```python
from archive_processing.tasks.core_tasks import dispatch_pdf_processing

# 触发PDF处理任务
result = dispatch_pdf_processing(task_id, use_parallel=True, chunk_size=20)
```

## 💡 最佳实践

### 1. 显式队列指定

当路由配置不可靠时，在 `apply_async()` 中显式指定队列是最可靠的方法：

```python
task.apply_async(
    args=[...],
    queue='pdf_processing',
    routing_key='pdf_processing'
)
```

### 2. 监控队列分布

定期检查任务是否被正确路由：

```bash
python scripts/celery_monitor.py status
```

### 3. 性能调优

- PDF worker并发数建议不超过CPU核心数的50%
- 监控内存使用，每个PDF进程约需512MB-1GB
- 根据负载动态调整并发数

## 🔮 后续优化

1. **调查路由配置问题**：虽然问题已解决，但可以进一步调查为什么 `CELERY_TASK_ROUTES` 配置没有自动生效

2. **自动化测试**：添加自动化测试来验证任务路由的正确性

3. **监控告警**：设置监控来检测任务是否被错误路由

4. **文档更新**：更新团队文档，说明新的任务路由机制

---

**✅ PDF队列路由问题已完全解决！**

现在PDF处理任务能够正确路由到专用队列，实现了资源隔离和性能优化的目标。🎉
