"""
中间件模块
"""
import time
import asyncio
from typing import Callable
from fastapi import Request, Response, HTTPException
from starlette.middleware.base import BaseHTTPMiddleware
import structlog

logger = structlog.get_logger(__name__)


class RequestLimitMiddleware(BaseHTTPMiddleware):
    """请求限制中间件"""
    
    def __init__(self, app, max_concurrent: int = 10):
        super().__init__(app)
        self.max_concurrent = max_concurrent
        self.semaphore = asyncio.Semaphore(max_concurrent)
        self.active_requests = 0
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # 检查并发限制
        if self.active_requests >= self.max_concurrent:
            logger.warning("请求被拒绝，超过并发限制", 
                          active_requests=self.active_requests,
                          max_concurrent=self.max_concurrent)
            raise HTTPException(status_code=429, detail="服务繁忙，请稍后重试")
        
        async with self.semaphore:
            self.active_requests += 1
            try:
                response = await call_next(request)
                return response
            finally:
                self.active_requests -= 1


class RequestTrackingMiddleware(BaseHTTPMiddleware):
    """请求追踪中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        start_time = time.time()
        
        # 生成请求 ID
        request_id = f"{int(start_time * 1000000)}"
        
        # 记录请求开始
        logger.info("请求开始", 
                   request_id=request_id,
                   method=request.method,
                   url=str(request.url),
                   client_ip=request.client.host if request.client else None)
        
        try:
            response = await call_next(request)
            
            # 记录请求完成
            processing_time = time.time() - start_time
            logger.info("请求完成", 
                       request_id=request_id,
                       status_code=response.status_code,
                       processing_time=processing_time)
            
            # 添加响应头
            response.headers["X-Request-ID"] = request_id
            response.headers["X-Processing-Time"] = f"{processing_time:.3f}"
            
            return response
            
        except Exception as e:
            # 记录请求错误
            processing_time = time.time() - start_time
            logger.error("请求失败", 
                        request_id=request_id,
                        error=str(e),
                        processing_time=processing_time,
                        exc_info=True)
            raise


class SecurityHeadersMiddleware(BaseHTTPMiddleware):
    """安全头中间件"""
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        response = await call_next(request)
        
        # 添加安全头
        response.headers["X-Content-Type-Options"] = "nosniff"
        response.headers["X-Frame-Options"] = "DENY"
        response.headers["X-XSS-Protection"] = "1; mode=block"
        response.headers["Referrer-Policy"] = "strict-origin-when-cross-origin"
        
        return response
