"""
OCR 服务数据模型
"""
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field, validator
from enum import Enum


class OCRMode(str, Enum):
    """OCR 模式枚举"""
    BASIC = "basic"
    ENHANCED = "enhanced"


class ImageFormat(str, Enum):
    """支持的图像格式"""
    JPEG = "jpeg"
    PNG = "png"
    WEBP = "webp"
    BMP = "bmp"
    TIFF = "tiff"


class OCRRequest(BaseModel):
    """OCR 请求模型"""
    mode: OCRMode = Field(default=OCRMode.BASIC, description="OCR 模式")
    max_attempts: int = Field(default=3, ge=1, le=10, description="最大尝试次数")
    target_text: Optional[str] = Field(default=None, description="目标文本 (用于提前退出)")
    extract_number: bool = Field(default=False, description="是否提取统一编号")
    collect_all_results: bool = Field(default=False, description="是否收集所有结果")
    
    @validator('target_text')
    def validate_target_text(cls, v):
        if v is not None and len(v.strip()) == 0:
            return None
        return v


class OCRTextResult(BaseModel):
    """OCR 文本识别结果"""
    text: str = Field(description="识别出的文本")
    confidence: Optional[float] = Field(default=None, description="置信度")
    method: str = Field(description="识别方法")
    processing_time: float = Field(description="处理时间 (秒)")


class OCRNumberResult(BaseModel):
    """OCR 编号提取结果"""
    number: Optional[str] = Field(description="提取的编号")
    method: str = Field(description="提取方法")
    confidence: Optional[float] = Field(default=None, description="置信度")


class OCRResponse(BaseModel):
    """OCR 响应模型"""
    success: bool = Field(description="是否成功")
    mode: OCRMode = Field(description="使用的 OCR 模式")
    
    # 基础模式结果
    text: Optional[str] = Field(default=None, description="识别的文本")
    
    # 增强模式结果
    text_results: Optional[List[OCRTextResult]] = Field(default=None, description="文本识别结果列表")
    number_results: Optional[List[OCRNumberResult]] = Field(default=None, description="编号提取结果列表")
    found_target: Optional[bool] = Field(default=None, description="是否找到目标文本")
    
    # 元数据
    processing_time: float = Field(description="总处理时间 (秒)")
    image_size: Optional[Dict[str, int]] = Field(default=None, description="图像尺寸")
    error: Optional[str] = Field(default=None, description="错误信息")


class HealthStatus(BaseModel):
    """健康状态模型"""
    status: str = Field(description="服务状态")
    timestamp: str = Field(description="检查时间")
    version: str = Field(description="服务版本")
    uptime: float = Field(description="运行时间 (秒)")
    ocr_engine_ready: bool = Field(description="OCR 引擎是否就绪")
    memory_usage: Dict[str, Any] = Field(description="内存使用情况")
    active_requests: int = Field(description="活跃请求数")


class MetricsResponse(BaseModel):
    """指标响应模型"""
    total_requests: int = Field(description="总请求数")
    successful_requests: int = Field(description="成功请求数")
    failed_requests: int = Field(description="失败请求数")
    average_processing_time: float = Field(description="平均处理时间")
    active_requests: int = Field(description="当前活跃请求数")
    uptime: float = Field(description="运行时间")
    memory_usage: Dict[str, Any] = Field(description="内存使用情况")
