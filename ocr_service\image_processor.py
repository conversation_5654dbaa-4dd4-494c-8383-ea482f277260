"""
图像处理模块
"""
import io
from typing import List, Tu<PERSON>, Optional
from PIL import Image, ImageEnhance, ImageFilter
import structlog

from .config import settings

logger = structlog.get_logger(__name__)


class ImageProcessor:
    """图像处理器"""
    
    @staticmethod
    def validate_image(image_data: bytes) -> Tuple[bool, Optional[str]]:
        """验证图像数据"""
        try:
            # 检查文件大小
            if len(image_data) > settings.max_image_size:
                return False, f"图像文件过大: {len(image_data)} bytes > {settings.max_image_size} bytes"
            
            # 尝试打开图像
            image = Image.open(io.BytesIO(image_data))
            
            # 检查图像格式
            if image.format.lower() not in ['jpeg', 'png', 'webp', 'bmp', 'tiff']:
                return False, f"不支持的图像格式: {image.format}"
            
            # 检查图像尺寸
            width, height = image.size
            if width * height > 50_000_000:  # 50M 像素
                return False, f"图像尺寸过大: {width}x{height}"
            
            return True, None
            
        except Exception as e:
            return False, f"图像验证失败: {str(e)}"
    
    @staticmethod
    def load_image(image_data: bytes) -> Image.Image:
        """加载图像"""
        try:
            image = Image.open(io.BytesIO(image_data))
            
            # 转换为 RGB 模式 (PaddleOCR 需要)
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            logger.debug("图像加载成功", 
                        size=image.size, 
                        mode=image.mode,
                        format=getattr(image, 'format', 'Unknown'))
            
            return image
            
        except Exception as e:
            logger.error("图像加载失败", error=str(e))
            raise ValueError(f"无法加载图像: {str(e)}")
    
    @staticmethod
    def prepare_standard_image(image: Image.Image) -> Image.Image:
        """准备标准图像 (用于基础 OCR)"""
        try:
            # 确保是 RGB 模式
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 基础预处理
            # 1. 适度锐化
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(1.2)
            
            # 2. 对比度增强
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.1)
            
            return image
            
        except Exception as e:
            logger.error("标准图像预处理失败", error=str(e))
            return image  # 返回原图像
    
    @staticmethod
    def get_enhanced_images(image: Image.Image, max_variants: int = 4) -> List[Image.Image]:
        """生成增强图像变体 (迁移自原有业务逻辑)

        CHANGE: [2025-07-03] 迁移原有的专业图像处理逻辑
        保持与 archive_processing/utils/image_utils.py 中 get_enhanced_images_for_paddle 一致
        """
        processed_images = []

        try:
            # 确保输入是 RGB (对应原有的 prepare_standard_image_for_paddle)
            if image.mode != 'RGB':
                rgb_image = image.convert('RGB')
            else:
                rgb_image = image.copy()

            # 1. 增强对比度 (对应原有逻辑)
            try:
                enhanced_contrast = ImageEnhance.Contrast(rgb_image).enhance(1.5)
                processed_images.append(enhanced_contrast)
            except Exception as e:
                logger.warning("生成增强对比度变体失败", error=str(e))

            # 2. 亮度增强 (对应原有逻辑)
            if len(processed_images) < max_variants:
                try:
                    enhanced_bright = ImageEnhance.Brightness(rgb_image).enhance(1.2)
                    processed_images.append(enhanced_bright)
                except Exception as e:
                    logger.warning("生成亮度增强变体失败", error=str(e))

            # 3. 锐化处理 (对应原有逻辑)
            if len(processed_images) < max_variants:
                try:
                    sharpened = rgb_image.filter(ImageFilter.SHARPEN)
                    processed_images.append(sharpened)
                except Exception as e:
                    logger.warning("生成锐化变体失败", error=str(e))

            # 4. 对比度和锐化结合 (对应原有逻辑)
            if len(processed_images) < max_variants:
                try:
                    sharpened = rgb_image.filter(ImageFilter.SHARPEN)
                    contrast_sharp = ImageEnhance.Contrast(sharpened).enhance(1.3)
                    processed_images.append(contrast_sharp)
                except Exception as e:
                    logger.warning("生成对比度锐化组合变体失败", error=str(e))

            logger.debug("生成增强图像变体完成",
                        original_size=image.size,
                        variants_count=len(processed_images),
                        method="migrated_business_logic")

            # 如果出错，至少返回原始RGB图像
            if not processed_images:
                processed_images.append(rgb_image)

            return processed_images[:max_variants]

        except Exception as e:
            logger.error("生成增强图像失败", error=str(e), exc_info=True)
            # 至少返回原图
            return [image] if not processed_images else processed_images


# 全局图像处理器实例
image_processor = ImageProcessor()
