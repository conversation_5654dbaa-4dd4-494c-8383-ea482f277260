"""
图像处理模块
"""
import io
from typing import List, Tu<PERSON>, Optional
from PIL import Image, ImageEnhance, ImageFilter
import structlog

from .config import settings

logger = structlog.get_logger(__name__)


class ImageProcessor:
    """图像处理器"""
    
    @staticmethod
    def validate_image(image_data: bytes) -> Tuple[bool, Optional[str]]:
        """验证图像数据"""
        try:
            # 检查文件大小
            if len(image_data) > settings.max_image_size:
                return False, f"图像文件过大: {len(image_data)} bytes > {settings.max_image_size} bytes"
            
            # 尝试打开图像
            image = Image.open(io.BytesIO(image_data))
            
            # 检查图像格式
            if image.format.lower() not in ['jpeg', 'png', 'webp', 'bmp', 'tiff']:
                return False, f"不支持的图像格式: {image.format}"
            
            # 检查图像尺寸
            width, height = image.size
            if width * height > 50_000_000:  # 50M 像素
                return False, f"图像尺寸过大: {width}x{height}"
            
            return True, None
            
        except Exception as e:
            return False, f"图像验证失败: {str(e)}"
    
    @staticmethod
    def load_image(image_data: bytes) -> Image.Image:
        """加载图像"""
        try:
            image = Image.open(io.BytesIO(image_data))
            
            # 转换为 RGB 模式 (PaddleOCR 需要)
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            logger.debug("图像加载成功", 
                        size=image.size, 
                        mode=image.mode,
                        format=getattr(image, 'format', 'Unknown'))
            
            return image
            
        except Exception as e:
            logger.error("图像加载失败", error=str(e))
            raise ValueError(f"无法加载图像: {str(e)}")
    
    @staticmethod
    def prepare_standard_image(image: Image.Image) -> Image.Image:
        """准备标准图像 (用于基础 OCR)"""
        try:
            # 确保是 RGB 模式
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 基础预处理
            # 1. 适度锐化
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(1.2)
            
            # 2. 对比度增强
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.1)
            
            return image
            
        except Exception as e:
            logger.error("标准图像预处理失败", error=str(e))
            return image  # 返回原图像
    
    @staticmethod
    def get_enhanced_images(image: Image.Image, max_variants: int = 3) -> List[Image.Image]:
        """生成增强图像变体 (用于增强 OCR)"""
        variants = []
        
        try:
            # 原图
            variants.append(ImageProcessor.prepare_standard_image(image))
            
            if max_variants > 1:
                # 变体 1: 高对比度
                try:
                    enhanced = image.copy()
                    if enhanced.mode != 'RGB':
                        enhanced = enhanced.convert('RGB')
                    
                    # 高对比度
                    enhancer = ImageEnhance.Contrast(enhanced)
                    enhanced = enhancer.enhance(1.5)
                    
                    # 锐化
                    enhancer = ImageEnhance.Sharpness(enhanced)
                    enhanced = enhancer.enhance(1.5)
                    
                    variants.append(enhanced)
                except Exception as e:
                    logger.warning("生成高对比度变体失败", error=str(e))
            
            if max_variants > 2:
                # 变体 2: 亮度调整
                try:
                    enhanced = image.copy()
                    if enhanced.mode != 'RGB':
                        enhanced = enhanced.convert('RGB')
                    
                    # 亮度增强
                    enhancer = ImageEnhance.Brightness(enhanced)
                    enhanced = enhancer.enhance(1.2)
                    
                    # 对比度
                    enhancer = ImageEnhance.Contrast(enhanced)
                    enhanced = enhancer.enhance(1.3)
                    
                    variants.append(enhanced)
                except Exception as e:
                    logger.warning("生成亮度调整变体失败", error=str(e))
            
            logger.debug("生成图像变体完成", 
                        original_size=image.size,
                        variants_count=len(variants))
            
            return variants[:max_variants]
            
        except Exception as e:
            logger.error("生成增强图像失败", error=str(e))
            # 至少返回原图
            return [image] if variants == [] else variants


# 全局图像处理器实例
image_processor = ImageProcessor()
