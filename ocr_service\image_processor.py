"""
图像处理模块
"""
import io
from typing import List, <PERSON><PERSON>, Optional
from PIL import Image, ImageEnhance, ImageFilter
import numpy as np

from .config import settings
from .logging_config import get_logger
from .image_config import image_config

logger = get_logger(__name__)


class ImageProcessor:
    """图像处理器"""
    
    @staticmethod
    def validate_image(image_data: bytes) -> Tuple[bool, Optional[str]]:
        """验证图像数据"""
        try:
            # 检查文件大小
            if len(image_data) > settings.max_image_size:
                return False, f"图像文件过大: {len(image_data)} bytes > {settings.max_image_size} bytes"
            
            # 尝试打开图像
            image = Image.open(io.BytesIO(image_data))
            
            # 检查图像格式
            if image.format.lower() not in ['jpeg', 'png', 'webp', 'bmp', 'tiff']:
                return False, f"不支持的图像格式: {image.format}"
            
            # 检查图像尺寸
            width, height = image.size
            if width * height > 50_000_000:  # 50M 像素
                return False, f"图像尺寸过大: {width}x{height}"
            
            return True, None
            
        except Exception as e:
            return False, f"图像验证失败: {str(e)}"
    
    @staticmethod
    def load_image(image_data: bytes) -> Image.Image:
        """加载图像"""
        try:
            image = Image.open(io.BytesIO(image_data))
            
            # 转换为 RGB 模式 (PaddleOCR 需要)
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            logger.debug("图像加载成功", 
                        size=image.size, 
                        mode=image.mode,
                        format=getattr(image, 'format', 'Unknown'))
            
            return image
            
        except Exception as e:
            logger.error("图像加载失败", error=str(e))
            raise ValueError(f"无法加载图像: {str(e)}")
    
    @staticmethod
    def prepare_standard_image(image: Image.Image) -> Image.Image:
        """准备标准图像 (用于基础 OCR)"""
        try:
            # 确保是 RGB 模式
            if image.mode != 'RGB':
                image = image.convert('RGB')
            
            # 基础预处理
            # 1. 适度锐化
            enhancer = ImageEnhance.Sharpness(image)
            image = enhancer.enhance(1.2)
            
            # 2. 对比度增强
            enhancer = ImageEnhance.Contrast(image)
            image = enhancer.enhance(1.1)
            
            return image
            
        except Exception as e:
            logger.error("标准图像预处理失败", error=str(e))
            return image  # 返回原图像
    
    @staticmethod
    def get_enhanced_images(image: Image.Image, max_variants: int = 4) -> List[Image.Image]:
        """生成增强图像变体 (优化版)

        CHANGE: [2025-07-03] 基于原有逻辑的优化版本
        - 保持与原有逻辑完全一致的4种变体
        - 增加图像质量检查和优化
        - 添加自适应参数调整
        """
        processed_images = []

        try:
            # 确保输入是 RGB (对应原有的 prepare_standard_image_for_paddle)
            if image.mode != 'RGB':
                rgb_image = image.convert('RGB')
            else:
                rgb_image = image.copy()

            # 图像质量分析 (用于自适应调整)
            img_array = np.array(rgb_image)
            brightness = np.mean(img_array)
            contrast = np.std(img_array)

            logger.debug("图像质量分析",
                        brightness=brightness,
                        contrast=contrast,
                        size=rgb_image.size)

            # 1. 增强对比度 (使用配置参数)
            try:
                contrast_factor = image_config.get_contrast_factor(contrast)
                enhanced_contrast = ImageEnhance.Contrast(rgb_image).enhance(contrast_factor)
                processed_images.append(enhanced_contrast)
                logger.debug("生成对比度增强变体",
                           factor=contrast_factor,
                           adaptive=image_config.enable_adaptive)
            except Exception as e:
                logger.warning("生成增强对比度变体失败", error=str(e))

            # 2. 亮度增强 (使用配置参数)
            if len(processed_images) < max_variants:
                try:
                    brightness_factor = image_config.get_brightness_factor(brightness)
                    enhanced_bright = ImageEnhance.Brightness(rgb_image).enhance(brightness_factor)
                    processed_images.append(enhanced_bright)
                    logger.debug("生成亮度增强变体",
                               factor=brightness_factor,
                               adaptive=image_config.enable_adaptive)
                except Exception as e:
                    logger.warning("生成亮度增强变体失败", error=str(e))

            # 3. 锐化处理 (可配置的增强锐化)
            if len(processed_images) < max_variants:
                try:
                    # 基础锐化 (保持与原方案一致)
                    sharpened = rgb_image.filter(ImageFilter.SHARPEN)

                    # 可选的高级锐化
                    if image_config.enable_unsharp_mask:
                        unsharp_params = image_config.get_unsharp_params()
                        sharpened = sharpened.filter(ImageFilter.UnsharpMask(
                            radius=unsharp_params['radius'],
                            percent=unsharp_params['percent'],
                            threshold=unsharp_params['threshold']
                        ))
                        method = "SHARPEN+UnsharpMask"
                    else:
                        method = "SHARPEN"

                    processed_images.append(sharpened)
                    logger.debug("生成锐化处理变体", method=method)
                except Exception as e:
                    logger.warning("生成锐化变体失败", error=str(e))
                    # 回退到基础锐化
                    try:
                        sharpened = rgb_image.filter(ImageFilter.SHARPEN)
                        processed_images.append(sharpened)
                    except:
                        pass

            # 4. 对比度和锐化结合 (使用配置参数)
            if len(processed_images) < max_variants:
                try:
                    # 先锐化，再增强对比度 (保持与原方案一致)
                    sharpened = rgb_image.filter(ImageFilter.SHARPEN)
                    contrast_sharp = ImageEnhance.Contrast(sharpened).enhance(image_config.contrast_sharp_factor)

                    # 可选的色彩饱和度微调
                    if image_config.enable_color_adjustment:
                        color_enhanced = ImageEnhance.Color(contrast_sharp).enhance(image_config.color_saturation_factor)
                        processed_images.append(color_enhanced)
                        method = "SHARPEN+Contrast+Color"
                    else:
                        processed_images.append(contrast_sharp)
                        method = "SHARPEN+Contrast"

                    logger.debug("生成对比度锐化组合变体", method=method)
                except Exception as e:
                    logger.warning("生成对比度锐化组合变体失败", error=str(e))
                    # 回退到原有逻辑
                    try:
                        sharpened = rgb_image.filter(ImageFilter.SHARPEN)
                        contrast_sharp = ImageEnhance.Contrast(sharpened).enhance(image_config.contrast_sharp_factor)
                        processed_images.append(contrast_sharp)
                    except:
                        pass

            # 质量验证：确保所有图像都是有效的
            if image_config.enable_quality_check:
                valid_images = []
                for i, img in enumerate(processed_images):
                    try:
                        # 检查图像尺寸和模式
                        if (img.size == rgb_image.size and
                            img.mode == 'RGB' and
                            image_config.is_valid_image_size(img.size[0], img.size[1])):
                            valid_images.append(img)
                        else:
                            logger.warning(f"图像变体 {i+1} 质量检查失败",
                                         size=img.size,
                                         mode=img.mode)
                    except Exception as e:
                        logger.warning(f"图像变体 {i+1} 验证失败", error=str(e))

                processed_images = valid_images
                logger.debug("质量检查完成",
                           original_count=len(processed_images) + (len(processed_images) - len(valid_images)),
                           valid_count=len(valid_images))
            else:
                logger.debug("跳过质量检查", reason="disabled_in_config")

            logger.debug("生成增强图像变体完成",
                        original_size=rgb_image.size,
                        variants_count=len(processed_images),
                        method="optimized_business_logic")

            # 如果出错，至少返回原始RGB图像
            if not processed_images:
                logger.warning("所有图像变体生成失败，返回原始图像")
                processed_images.append(rgb_image)

            return processed_images[:max_variants]

        except Exception as e:
            logger.error("生成增强图像失败", error=str(e), exc_info=True)
            # 至少返回原图
            try:
                if image.mode != 'RGB':
                    return [image.convert('RGB')]
                else:
                    return [image.copy()]
            except:
                return [image]


# 全局图像处理器实例
image_processor = ImageProcessor()
