#!/usr/bin/env python3
"""
OCR 微服务启动脚本
解决模块导入问题
"""
import sys
import os

# 确保当前目录在 Python 路径中
current_dir = os.path.dirname(os.path.abspath(__file__))
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)

# 设置环境变量
os.environ.setdefault('PYTHONPATH', current_dir)

if __name__ == "__main__":
    import uvicorn
    
    # 导入应用
    from main import app
    
    # 启动服务
    uvicorn.run(
        app,
        host="0.0.0.0",
        port=8001,
        workers=1,
        log_level="info"
    )
