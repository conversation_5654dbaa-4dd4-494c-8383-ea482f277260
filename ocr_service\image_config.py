"""
图像处理配置模块
可以通过环境变量调整图像处理参数
"""
import os
from typing import Dict, Any


class ImageProcessingConfig:
    """图像处理配置类"""
    
    def __init__(self):
        # 基础参数 (与原方案保持一致)
        self.contrast_factor = float(os.getenv('OCR_CONTRAST_FACTOR', '1.5'))
        self.brightness_factor = float(os.getenv('OCR_BRIGHTNESS_FACTOR', '1.2'))
        self.contrast_sharp_factor = float(os.getenv('OCR_CONTRAST_SHARP_FACTOR', '1.3'))
        
        # 自适应增强参数
        self.enable_adaptive = os.getenv('OCR_ENABLE_ADAPTIVE', 'true').lower() == 'true'
        self.adaptive_contrast_threshold = float(os.getenv('OCR_ADAPTIVE_CONTRAST_THRESHOLD', '30'))
        self.adaptive_brightness_threshold = float(os.getenv('OCR_ADAPTIVE_BRIGHTNESS_THRESHOLD', '100'))
        
        # 自适应增强因子
        self.adaptive_contrast_factor = float(os.getenv('OCR_ADAPTIVE_CONTRAST_FACTOR', '1.7'))
        self.adaptive_brightness_factor = float(os.getenv('OCR_ADAPTIVE_BRIGHTNESS_FACTOR', '1.4'))
        
        # 高级处理选项
        self.enable_unsharp_mask = os.getenv('OCR_ENABLE_UNSHARP_MASK', 'true').lower() == 'true'
        self.unsharp_radius = float(os.getenv('OCR_UNSHARP_RADIUS', '1.0'))
        self.unsharp_percent = int(os.getenv('OCR_UNSHARP_PERCENT', '120'))
        self.unsharp_threshold = int(os.getenv('OCR_UNSHARP_THRESHOLD', '2'))
        
        # 色彩调整
        self.enable_color_adjustment = os.getenv('OCR_ENABLE_COLOR_ADJUSTMENT', 'true').lower() == 'true'
        self.color_saturation_factor = float(os.getenv('OCR_COLOR_SATURATION_FACTOR', '0.9'))
        
        # 质量控制
        self.enable_quality_check = os.getenv('OCR_ENABLE_QUALITY_CHECK', 'true').lower() == 'true'
        self.min_image_size = int(os.getenv('OCR_MIN_IMAGE_SIZE', '50'))
        self.max_image_size = int(os.getenv('OCR_MAX_IMAGE_SIZE', '10000'))
    
    def get_contrast_factor(self, image_contrast: float) -> float:
        """获取对比度增强因子"""
        if self.enable_adaptive and image_contrast < self.adaptive_contrast_threshold:
            return self.adaptive_contrast_factor
        return self.contrast_factor
    
    def get_brightness_factor(self, image_brightness: float) -> float:
        """获取亮度增强因子"""
        if self.enable_adaptive and image_brightness < self.adaptive_brightness_threshold:
            return self.adaptive_brightness_factor
        return self.brightness_factor
    
    def get_unsharp_params(self) -> Dict[str, Any]:
        """获取锐化掩模参数"""
        return {
            'radius': self.unsharp_radius,
            'percent': self.unsharp_percent,
            'threshold': self.unsharp_threshold
        }
    
    def is_valid_image_size(self, width: int, height: int) -> bool:
        """检查图像尺寸是否有效"""
        return (self.min_image_size <= width <= self.max_image_size and 
                self.min_image_size <= height <= self.max_image_size)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典格式"""
        return {
            'contrast_factor': self.contrast_factor,
            'brightness_factor': self.brightness_factor,
            'contrast_sharp_factor': self.contrast_sharp_factor,
            'enable_adaptive': self.enable_adaptive,
            'adaptive_contrast_threshold': self.adaptive_contrast_threshold,
            'adaptive_brightness_threshold': self.adaptive_brightness_threshold,
            'adaptive_contrast_factor': self.adaptive_contrast_factor,
            'adaptive_brightness_factor': self.adaptive_brightness_factor,
            'enable_unsharp_mask': self.enable_unsharp_mask,
            'unsharp_radius': self.unsharp_radius,
            'unsharp_percent': self.unsharp_percent,
            'unsharp_threshold': self.unsharp_threshold,
            'enable_color_adjustment': self.enable_color_adjustment,
            'color_saturation_factor': self.color_saturation_factor,
            'enable_quality_check': self.enable_quality_check,
            'min_image_size': self.min_image_size,
            'max_image_size': self.max_image_size
        }


# 全局配置实例
image_config = ImageProcessingConfig()


def reload_config():
    """重新加载配置"""
    global image_config
    image_config = ImageProcessingConfig()


def print_config():
    """打印当前配置"""
    config_dict = image_config.to_dict()
    print("📋 当前图像处理配置:")
    print("-" * 40)
    
    print("基础参数:")
    print(f"  对比度因子: {config_dict['contrast_factor']}")
    print(f"  亮度因子: {config_dict['brightness_factor']}")
    print(f"  对比度锐化因子: {config_dict['contrast_sharp_factor']}")
    
    print("\n自适应增强:")
    print(f"  启用自适应: {config_dict['enable_adaptive']}")
    if config_dict['enable_adaptive']:
        print(f"  对比度阈值: {config_dict['adaptive_contrast_threshold']}")
        print(f"  亮度阈值: {config_dict['adaptive_brightness_threshold']}")
        print(f"  自适应对比度因子: {config_dict['adaptive_contrast_factor']}")
        print(f"  自适应亮度因子: {config_dict['adaptive_brightness_factor']}")
    
    print("\n高级处理:")
    print(f"  启用锐化掩模: {config_dict['enable_unsharp_mask']}")
    if config_dict['enable_unsharp_mask']:
        print(f"  锐化半径: {config_dict['unsharp_radius']}")
        print(f"  锐化强度: {config_dict['unsharp_percent']}%")
        print(f"  锐化阈值: {config_dict['unsharp_threshold']}")
    
    print(f"  启用色彩调整: {config_dict['enable_color_adjustment']}")
    if config_dict['enable_color_adjustment']:
        print(f"  色彩饱和度因子: {config_dict['color_saturation_factor']}")
    
    print("\n质量控制:")
    print(f"  启用质量检查: {config_dict['enable_quality_check']}")
    print(f"  最小图像尺寸: {config_dict['min_image_size']}")
    print(f"  最大图像尺寸: {config_dict['max_image_size']}")


if __name__ == "__main__":
    print_config()
