# archive_processing/utils/ocr_utils.py
"""
OCR 工具模块

此模块包含与光学字符识别 (OCR) 执行相关的功能，包括：
1. 初始化 OCR 引擎 (PaddleOCR)
2. 在图像上运行 Tesseract OCR
3. 在图像上运行 PaddleOCR
4. 统一的 OCR 执行入口点
5. 页面级 OCR 和文本提取流程

从 pdf_processor_usefull.py 中的 PDFProcessor 类提取和重构。
"""

import logging
import time
from typing import Optional, List, Tuple, Dict, Union, TYPE_CHECKING, Any
from PIL import Image
import pytesseract  # Tesseract
import warnings
import numpy as np  # <-- 添加 numpy 导入

# For type checking only - avoids variable in type expression error
if TYPE_CHECKING:
    from paddleocr import PaddleOCR

# CHANGE: [2025-07-03] OCR 微服务化改造
# 不再直接导入 PaddleOCR，改为通过 HTTP API 调用
import requests
import io
from requests.adapters import HTTPAdapter
from urllib3.util.retry import Retry

# OCR 服务配置
OCR_SERVICE_URL = "http://ocr-service:8001"  # Docker 内部服务名
OCR_SERVICE_TIMEOUT = 30.0  # 请求超时时间
OCR_SERVICE_RETRIES = 3  # 重试次数
OCR_SERVICE_HEALTH_CHECK_TIMEOUT = 5.0  # 健康检查超时时间
OCR_SERVICE_CIRCUIT_BREAKER_THRESHOLD = 5  # 熔断器阈值
OCR_SERVICE_CIRCUIT_BREAKER_TIMEOUT = 60.0  # 熔断器超时时间

# 图像处理策略配置
# True: 方案1 - 微服务内生成图像变体 (现代化，网络效率高)
# False: 方案2 - 主应用预处理，逐个发送 (保守，零风险)
USE_MICROSERVICE_IMAGE_PROCESSING = False  # 默认使用方案2

# 标记 PaddleOCR 通过微服务可用
PADDLE_OCR_AVAILABLE = True  # 假设 OCR 服务可用，实际可用性在运行时检查

# 导入本项目其他工具模块
from . import image_utils
from . import text_utils
from archive_processing.dto.result_dtos import UnifiedNumberResult

logger = logging.getLogger(__name__)


# --- OCR 微服务客户端 ---

class CircuitBreaker:
    """简单的熔断器实现"""

    def __init__(self, failure_threshold: int = 5, timeout: float = 60.0):
        self.failure_threshold = failure_threshold
        self.timeout = timeout
        self.failure_count = 0
        self.last_failure_time = None
        self.state = "CLOSED"  # CLOSED, OPEN, HALF_OPEN

    def call(self, func, *args, **kwargs):
        """执行函数调用，带熔断器保护"""
        if self.state == "OPEN":
            if time.time() - self.last_failure_time > self.timeout:
                self.state = "HALF_OPEN"
                logger.info("熔断器进入半开状态，尝试恢复")
            else:
                raise Exception("熔断器开启，服务暂时不可用")

        try:
            result = func(*args, **kwargs)
            if self.state == "HALF_OPEN":
                self.state = "CLOSED"
                self.failure_count = 0
                logger.info("熔断器恢复正常状态")
            return result
        except Exception as e:
            self.failure_count += 1
            self.last_failure_time = time.time()

            if self.failure_count >= self.failure_threshold:
                self.state = "OPEN"
                logger.warning(f"熔断器开启，失败次数: {self.failure_count}")

            raise e


class OCRServiceClient:
    """OCR 微服务客户端 (增强版)"""

    def __init__(self):
        self.base_url = OCR_SERVICE_URL
        self.timeout = OCR_SERVICE_TIMEOUT
        self.session = requests.Session()
        self.circuit_breaker = CircuitBreaker(
            failure_threshold=OCR_SERVICE_CIRCUIT_BREAKER_THRESHOLD,
            timeout=OCR_SERVICE_CIRCUIT_BREAKER_TIMEOUT
        )
        self._last_health_check = 0
        self._health_check_interval = 30.0  # 30秒检查一次
        self._is_healthy = None

        # 配置重试策略
        retry_strategy = Retry(
            total=OCR_SERVICE_RETRIES,
            status_forcelist=[429, 500, 502, 503, 504],
            method_whitelist=["HEAD", "GET", "POST"],
            backoff_factor=1,
            raise_on_status=False  # 不要在重试后抛出异常
        )
        adapter = HTTPAdapter(max_retries=retry_strategy)
        self.session.mount("http://", adapter)
        self.session.mount("https://", adapter)

    def is_available(self) -> bool:
        """检查 OCR 服务是否可用 (带缓存)"""
        current_time = time.time()

        # 如果最近检查过且结果为健康，直接返回
        if (self._is_healthy is True and
            current_time - self._last_health_check < self._health_check_interval):
            return True

        try:
            def _health_check():
                response = self.session.get(
                    f"{self.base_url}/health",
                    timeout=OCR_SERVICE_HEALTH_CHECK_TIMEOUT
                )
                if response.status_code == 200:
                    health_data = response.json()
                    return health_data.get('ocr_engine_ready', False)
                return False

            # 使用熔断器保护健康检查
            is_healthy = self.circuit_breaker.call(_health_check)

            self._is_healthy = is_healthy
            self._last_health_check = current_time

            if is_healthy:
                logger.debug("OCR 服务健康检查通过")
            else:
                logger.warning("OCR 服务健康检查失败: 引擎未就绪")

            return is_healthy

        except Exception as e:
            logger.warning(f"OCR 服务健康检查失败: {e}")
            self._is_healthy = False
            self._last_health_check = current_time
            return False

    def recognize_basic(self, image: Image.Image) -> str:
        """基础 OCR 识别 (带熔断器保护)"""
        def _recognize():
            # 将图像转换为字节流
            img_buffer = io.BytesIO()
            image.save(img_buffer, format='PNG')
            img_buffer.seek(0)

            # 准备请求数据
            files = {'file': ('image.png', img_buffer, 'image/png')}
            data = {'mode': 'basic'}

            # 发送请求
            response = self.session.post(
                f"{self.base_url}/ocr",
                files=files,
                data=data,
                timeout=self.timeout
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('success', False):
                    return result.get('text', '')
                else:
                    error_msg = result.get('error', 'Unknown error')
                    logger.warning(f"OCR 服务返回失败: {error_msg}")
                    raise Exception(f"OCR 处理失败: {error_msg}")
            elif response.status_code == 429:
                logger.warning("OCR 服务繁忙，请求被限流")
                raise Exception("服务繁忙，请稍后重试")
            elif response.status_code >= 500:
                logger.error(f"OCR 服务内部错误: {response.status_code}")
                raise Exception(f"服务内部错误: {response.status_code}")
            else:
                logger.error(f"OCR 服务请求失败: {response.status_code} - {response.text}")
                raise Exception(f"请求失败: {response.status_code}")

        try:
            return self.circuit_breaker.call(_recognize)
        except Exception as e:
            logger.error(f"OCR 基础识别失败: {e}")
            return ""

    def recognize_enhanced(self, images: List[Image.Image], max_attempts: int = 3) -> List[str]:
        """增强 OCR 识别 (方案2: 逐个发送预处理图像)"""
        results = []

        if not images:
            return [""] * max_attempts

        # 逐个处理主应用预处理的图像
        for i, image in enumerate(images[:max_attempts]):
            if not image:
                results.append("")
                continue

            try:
                # 对每个预处理图像调用基础识别
                text = self.recognize_basic(image)
                results.append(text)

                logger.debug(f"增强识别尝试 {i+1}/{len(images)}: {'成功' if text else '失败'}")

            except Exception as e:
                logger.error(f"增强识别尝试 {i+1} 失败: {e}")
                results.append("")

        # 确保返回正确数量的结果
        while len(results) < max_attempts:
            results.append("")

        return results[:max_attempts]

    def recognize_enhanced_v2(self, images: List[Image.Image], max_attempts: int = 3) -> List[str]:
        """增强 OCR 识别 (方案1: 微服务内生成变体) - 备用实现"""
        def _recognize_enhanced():
            if not images:
                return []

            image = images[0]  # 使用第一个图像

            # 将图像转换为字节流
            img_buffer = io.BytesIO()
            image.save(img_buffer, format='PNG')
            img_buffer.seek(0)

            # 准备请求数据
            files = {'file': ('image.png', img_buffer, 'image/png')}
            data = {
                'mode': 'enhanced',
                'max_attempts': max_attempts
            }

            # 发送请求
            response = self.session.post(
                f"{self.base_url}/ocr",
                files=files,
                data=data,
                timeout=self.timeout
            )

            if response.status_code == 200:
                result = response.json()
                if result.get('success', False):
                    text_results = result.get('text_results', [])
                    return [text_result.get('text', '') for text_result in text_results]
                else:
                    error_msg = result.get('error', 'Unknown error')
                    logger.warning(f"OCR 增强识别失败: {error_msg}")
                    raise Exception(f"OCR 增强处理失败: {error_msg}")
            elif response.status_code == 429:
                logger.warning("OCR 服务繁忙，增强识别请求被限流")
                raise Exception("服务繁忙，请稍后重试")
            elif response.status_code >= 500:
                logger.error(f"OCR 服务内部错误: {response.status_code}")
                raise Exception(f"服务内部错误: {response.status_code}")
            else:
                logger.error(f"OCR 增强识别请求失败: {response.status_code} - {response.text}")
                raise Exception(f"请求失败: {response.status_code}")

        try:
            results = self.circuit_breaker.call(_recognize_enhanced)

            # 确保返回正确数量的结果
            while len(results) < max_attempts:
                results.append("")

            return results[:max_attempts]

        except Exception as e:
            logger.error(f"OCR 增强识别失败: {e}")
            return [""] * max_attempts


# 全局 OCR 客户端实例
_ocr_client = OCRServiceClient()


# --- OCR 引擎初始化 (兼容性保持) ---


def init_paddle_ocr(use_paddle: bool = True) -> Optional[Any]:
    """初始化PaddleOCR引擎 (微服务模式)。

    CHANGE: [2025-07-03] 微服务化改造
    现在返回 OCR 客户端实例而不是 PaddleOCR 引擎实例。

    Args:
        use_paddle: 是否尝试使用PaddleOCR (通过微服务)。

    Returns:
        OCR 客户端实例，如果不可用或初始化失败则返回 None。
    """
    if not use_paddle:
        logger.info("未请求使用PaddleOCR。")
        return None

    try:
        logger.info("检查 OCR 微服务可用性...")

        # 检查 OCR 服务是否可用
        if _ocr_client.is_available():
            logger.info("OCR 微服务连接成功，引擎就绪。")
            return _ocr_client  # 返回客户端实例
        else:
            logger.warning("OCR 微服务不可用，将回退到 Tesseract。")
            return None

    except Exception as e:
        logger.error(f"OCR 微服务连接失败: {e}", exc_info=True)
        logger.warning("无法连接到 OCR 微服务，将回退到 Tesseract。")
        return None


# --- OCR 执行函数 ---


def run_tesseract_basic(image: Image.Image, config: str = None) -> str:
    """在单个预处理图像上运行 Tesseract OCR。

    Args:
        image: 经过 Tesseract 优化的预处理图像 (PIL.Image 对象)。
        config: Tesseract 配置字符串。如果为 None，使用默认配置。

    Returns:
        识别出的文本字符串，失败则返回空字符串。
    """
    if not image:
        return ""

    effective_config = config or "--oem 1 --psm 6 -l chi_sim+eng --dpi 150"

    try:
        logger.debug(f"运行 Tesseract (Basic) 使用配置: {effective_config}")
        text = pytesseract.image_to_string(image, config=effective_config)
        result = text.strip()
        logger.debug(f"Tesseract (Basic) 结果片段: {result[:50]}...")
        return result
    except Exception as e:
        logger.error(f"Tesseract OCR (Basic) 失败: {e}")
        return ""


def run_tesseract_enhanced(
    images: List[Image.Image], configs: List[str] = None
) -> List[str]:
    """在多个图像版本上运行 Tesseract OCR (用于增强识别)。

    Args:
        images: 预处理后的图像列表 (PIL.Image 对象)。
        configs: 与图像列表对应的 Tesseract 配置字符串列表。
                 如果为 None，将使用默认配置。

    Returns:
        每个图像识别出的文本列表 (失败则为空字符串)。
    """
    results = []
    if not images:
        return results

    # 如果未提供配置，为每个图像生成默认配置
    if configs is None:
        default_config = "--oem 1 --psm 6 -l chi_sim+eng --dpi 150"
        configs = [default_config] * len(images)
    elif len(configs) != len(images):
        logger.warning(
            f"提供的 Tesseract 配置数量 ({len(configs)}) 与图像数量 ({len(images)}) 不匹配。将尝试使用第一个配置。"
        )
        configs = [configs[0]] * len(images)  # 使用第一个配置填充

    for i, (image, config) in enumerate(zip(images, configs)):
        try:
            logger.debug(
                f"运行 Tesseract (增强尝试 {i+1}/{len(images)}) 使用配置: {config}"
            )
            text = pytesseract.image_to_string(image, config=config)
            results.append(text.strip())
            logger.debug(f"Tesseract (增强尝试 {i+1}) 结果片段: {text[:50]}...")
        except Exception as e:
            logger.error(f"Tesseract OCR 增强尝试 {i+1} 失败: {e}")
            results.append("")  # 添加空字符串表示失败

    return results


def run_paddle_basic(image: Image.Image, paddle_engine: Any) -> str:
    """在单个预处理图像上运行 PaddleOCR (微服务模式)。

    CHANGE: [2025-07-03] 微服务化改造
    现在通过 HTTP API 调用 OCR 微服务而不是直接调用 PaddleOCR。

    Args:
        image: 经过 PaddleOCR 优化的预处理图像 (通常是 RGB)。
        paddle_engine: OCR 客户端实例 (不再是 PaddleOCR 引擎)。

    Returns:
        识别出的文本字符串，失败则返回空字符串。
    """
    if not image or not paddle_engine:
        logger.warning("运行 PaddleOCR (Basic) 的输入图像或客户端无效。")
        return ""

    try:
        logger.debug("运行 PaddleOCR (Basic) 通过微服务...")

        # 通过微服务客户端执行识别
        text = paddle_engine.recognize_basic(image)

        if text:
            logger.debug(f"PaddleOCR (Basic - 微服务) 结果片段: {text[:50]}...")
        else:
            logger.debug("PaddleOCR (Basic - 微服务) 未返回有效结果。")

        return text

    except Exception as e:
        logger.error(f"PaddleOCR (Basic - 微服务) 失败: {e}", exc_info=True)
        return ""


def run_paddle_enhanced(
    images: List[Image.Image], paddle_engine: Any
) -> List[str]:
    """在多个图像版本上运行 PaddleOCR (微服务模式)。

    CHANGE: [2025-07-03] 微服务化改造 - 保持原有图像处理逻辑
    主应用仍然负责图像预处理，微服务只负责 OCR 识别

    Args:
        images: 预处理后的图像列表 (由主应用的 image_utils 生成)。
        paddle_engine: OCR 客户端实例 (不再是 PaddleOCR 引擎)。

    Returns:
        每个图像识别出的文本列表 (失败则为空字符串)。
    """
    if not images or not paddle_engine:
        logger.warning("运行 PaddleOCR (Enhanced) 的输入图像列表或客户端无效。")
        return [""] * len(images or [])

    ocr_results = []

    try:
        logger.debug("运行 PaddleOCR (Enhanced) 通过微服务...")

        # 保持原有逻辑：逐个处理主应用预处理的图像
        for i, image in enumerate(images):
            if not image:
                logger.warning(f"PaddleOCR (Enhanced) 尝试 {i+1}/{len(images)} 的图像无效，跳过。")
                ocr_results.append("")
                continue

            try:
                # 对每个预处理图像调用基础识别
                text = paddle_engine.recognize_basic(image)
                ocr_results.append(text)

                if text:
                    logger.debug(f"PaddleOCR (增强尝试 {i+1}) 结果片段: {text[:50]}...")
                else:
                    logger.debug(f"PaddleOCR (增强尝试 {i+1}) 未返回有效文本。")

            except Exception as e:
                logger.error(f"PaddleOCR 增强尝试 {i+1} 失败: {e}")
                ocr_results.append("")

        logger.debug(f"PaddleOCR (Enhanced - 微服务) 完成，获得 {len(ocr_results)} 个结果")

        return ocr_results

    except Exception as e:
        logger.error(f"PaddleOCR (Enhanced - 微服务) 失败: {e}", exc_info=True)
        return [""] * len(images)


# [这里将添加 run_ocr_on_image 函数]


# --- 页面级 OCR 流程 ---

# [这里将添加 ocr_page_and_extract 函数]


# --- 带控制逻辑的 OCR 调用 ---


def perform_basic_ocr(
    image: Image.Image,
    engine: str,
    ocr_cache: Dict[str, str],
    enable_cache: bool,
    paddle_engine: Optional[Any] = None,
    use_paddle: bool = False,
    config: Optional[str] = None,
    page_num: Optional[int] = None,
) -> str:
    """执行基础OCR，包括缓存检查、引擎选择和回退逻辑。

    Args:
        image: 原始 PIL 图像。
        engine: OCR 引擎选择 ("auto", "paddle", "tesseract")。
        ocr_cache: OCR 结果缓存字典。
        enable_cache: 是否启用缓存。
        paddle_engine: 已初始化的 PaddleOCR 引擎 (如果 use_paddle=True)。
        use_paddle: 是否允许使用 PaddleOCR。
        config: Tesseract 配置字符串 (可选)。
        page_num: 页码 (用于日志和缓存键，可选)。

    Returns:
        识别出的文本字符串。
    """
    if not image:
        return ""

    img_hash = None
    # 缓存检查
    if page_num is not None and enable_cache:
        try:
            img_hash = image_utils.image_hash(image)
            if img_hash in ocr_cache:
                logger.debug(f"第{page_num+1}页OCR结果命中缓存")
                return ocr_cache[img_hash]
        except Exception as hash_e:
            logger.warning(
                f"计算或检查图像哈希失败 {f'(页码:{page_num})' if page_num else ''}: {hash_e}"
            )
            # 即使哈希失败，也继续尝试OCR，但不使用缓存
            enable_cache = False

    text_result = ""
    try:
        # 自动选择或指定PaddleOCR
        should_try_paddle = (
            (engine.lower() == "auto" or engine.lower() == "paddle")
            and use_paddle
            and paddle_engine is not None
        )

        if should_try_paddle:
            # 1. 获取 Paddle 标准预处理图像
            processed_image = image_utils.prepare_standard_image_for_paddle(image)
            if processed_image:
                # 2. 运行基础 Paddle OCR
                text_result = run_paddle_basic(processed_image, paddle_engine)
            else:
                logger.warning(
                    f"无法为 PaddleOCR 生成预处理图像 {f'(页码:{page_num})' if page_num else ''}"
                )

            # 检查Paddle结果，如果为空且是auto模式，则回退到Tesseract
            if not text_result and engine.lower() == "auto":
                logger.info(
                    f"PaddleOCR 未返回有效结果或图像准备失败，自动回退到 Tesseract..."
                )
                # Fallthrough to Tesseract block
                pass  # 继续执行下面的 Tesseract 逻辑
            elif text_result:
                # Paddle 成功 (或指定了Paddle但图像准备失败导致text_result为空)
                pass  # 使用 text_result
            else:
                # 指定了 Paddle，但准备失败或执行失败，返回空
                return ""

        # Tesseract模式 (作为指定模式、回退模式、或 Paddle 失败后的 Fallthrough)
        if not text_result and (
            engine.lower() == "tesseract" or engine.lower() == "auto"
        ):
            # 1. 获取 Tesseract 标准预处理图像
            processed_image = image_utils.prepare_standard_image_for_tesseract(image)
            if processed_image:
                # 2. 运行基础 Tesseract OCR
                text_result = run_tesseract_basic(processed_image, config=config)
            else:
                logger.warning(
                    f"无法为 Tesseract 生成预处理图像 {f'(页码:{page_num})' if page_num else ''}"
                )
                text_result = ""  # 图像准备失败

        # 缓存结果 (如果成功识别出文本且缓存启用)
        if text_result and page_num is not None and enable_cache and img_hash:
            try:
                ocr_cache[img_hash] = text_result
            except Exception as cache_e:
                logger.warning(
                    f"写入OCR缓存失败 {f'(页码:{page_num})' if page_num else ''}: {cache_e}"
                )

        # 记录日志（降低频率）
        if text_result and page_num and page_num % 200 == 0:
            log_engine = "Paddle" if should_try_paddle and text_result else "Tesseract"
            logger.debug(
                f"第{page_num+1}页的 Basic OCR ({log_engine}) 结果片段: {text_result[:50]}..."
            )

        return text_result

    except Exception as e:
        logger.error(
            f"执行基础 OCR 失败{f'(页码:{page_num})' if page_num else ''}: {e}",
            exc_info=True,
        )
        return ""  # 发生意外错误时返回空字符串


def perform_enhanced_ocr(
    image: Image.Image,
    use_paddle: bool,
    paddle_engine: Optional[Any],
    target_text: Optional[str] = None,
    extract_number: bool = False,
    max_attempts: int = 3,
    collect_all_results: bool = False,
    tesseract_configs: Optional[List[str]] = None,  # 用于 Tesseract 的配置列表
) -> Union[bool, List[UnifiedNumberResult]]:
    """执行增强OCR，尝试多种图像处理和OCR配置以提高识别率。

    Args:
        image: 原始 PIL 图像。
        use_paddle: 是否优先使用 PaddleOCR。
        paddle_engine: 已初始化的 PaddleOCR 引擎实例。
        target_text: 用于提前退出的目标文本 (可选)。
        extract_number: 是否尝试提取统一编号。
        max_attempts: 最大尝试次数。
        collect_all_results: 是否收集所有结果 (如果为False，找到target_text即返回True)。
        tesseract_configs: Tesseract 的配置列表 (可选)。

    Returns:
        如果 collect_all_results=False 且找到 target_text，返回 True。
        如果 extract_number=True，返回 UnifiedNumberResult 列表。
        否则返回 False。
    """
    results: List[UnifiedNumberResult] = []
    if not image:
        return [] if extract_number else False

    try:
        if use_paddle and paddle_engine is not None:
            # --- PaddleOCR 增强模式 ---
            logger.debug("执行 PaddleOCR 增强识别...")
            # 1. 获取 Paddle 优化的增强图像列表
            processed_images = image_utils.get_enhanced_images_for_paddle(image)
            if not processed_images:
                logger.warning("无法为 PaddleOCR 生成增强图像列表")
                return [] if extract_number else False

            # 2. 运行增强 Paddle OCR 在多个图像上
            ocr_texts = run_paddle_enhanced(
                processed_images[:max_attempts], paddle_engine
            )

            # 3. 处理结果
            for i, text in enumerate(ocr_texts):
                if not text:
                    if extract_number:
                        results.append(
                            UnifiedNumberResult(None, f"PaddleOCR增强_{i+1} (失败)")
                        )
                    continue

                if target_text and target_text in text:
                    if not collect_all_results:
                        return True

                if extract_number:
                    number = text_utils.extract_unified_number(text, target_text)
                    method_name = f"PaddleOCR增强_{i+1}"
                    results.append(UnifiedNumberResult(number, method_name))

        else:
            # --- Tesseract 增强模式 ---
            logger.debug("执行 Tesseract 增强识别...")
            # 1. 获取 Tesseract 优化的增强图像列表
            processed_images = image_utils.get_enhanced_images_for_tesseract(image)
            if not processed_images:
                logger.warning("无法为 Tesseract 生成增强图像列表")
                return [] if extract_number else False

            # 2. 准备 Tesseract 配置 (如果未提供，使用默认)
            configs = tesseract_configs
            if configs is None:
                default_config = "--oem 1 --psm 6 -l chi_sim+eng --dpi 150"
                configs = [default_config] * max_attempts
            elif len(configs) < max_attempts:
                configs.extend(
                    [configs[-1]] * (max_attempts - len(configs))
                )  # 用最后一个配置填充

            # 3. 运行增强 Tesseract OCR 在多个图像上
            ocr_texts = run_tesseract_enhanced(
                processed_images[:max_attempts], configs[:max_attempts]
            )

            # 4. 处理结果
            for i, text in enumerate(ocr_texts):
                if not text:
                    if extract_number:
                        results.append(
                            UnifiedNumberResult(None, f"Tesseract增强_{i+1} (失败)")
                        )
                    continue

                if target_text and target_text in text:
                    if not collect_all_results:
                        return True

                if extract_number:
                    number = text_utils.extract_unified_number(text, target_text)
                    method_name = f"Tesseract增强_{i+1}"
                    results.append(UnifiedNumberResult(number, method_name))

        # 返回最终结果
        return results if extract_number else False

    except Exception as e:
        logger.error(f"执行增强 OCR 失败: {e}", exc_info=True)
        return [] if extract_number else False


# --- 结果验证 ---

# [这里可以考虑添加 validate_and_choose_number 函数]
