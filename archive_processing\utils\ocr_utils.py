# archive_processing/utils/ocr_utils.py
"""
OCR 工具模块

此模块包含与光学字符识别 (OCR) 执行相关的功能，包括：
1. 初始化 OCR 引擎 (PaddleOCR)
2. 在图像上运行 Tesseract OCR
3. 在图像上运行 PaddleOCR
4. 统一的 OCR 执行入口点
5. 页面级 OCR 和文本提取流程

从 pdf_processor_usefull.py 中的 PDFProcessor 类提取和重构。
"""

import logging
from typing import Optional, List, Tuple, Dict, Union, TYPE_CHECKING, Any
from PIL import Image
import pytesseract  # Tesseract
import warnings
import numpy as np  # <-- 添加 numpy 导入

# For type checking only - avoids variable in type expression error
if TYPE_CHECKING:
    from paddleocr import PaddleOCR

# 尝试导入 PaddleOCR (可选依赖)
try:
    from paddleocr import PaddleOCR

    PADDLE_OCR_AVAILABLE = True
    warnings.filterwarnings("ignore", category=UserWarning, module="paddle")
    paddle_logger = logging.getLogger("ppocr")
    if paddle_logger:
        paddle_logger.setLevel(logging.INFO)
except ImportError:
    PaddleOCR = None  # 定义为 None 以便类型检查
    PADDLE_OCR_AVAILABLE = False

# 导入本项目其他工具模块
from . import image_utils
from . import text_utils
from archive_processing.dto.result_dtos import UnifiedNumberResult

logger = logging.getLogger(__name__)


# --- OCR 引擎初始化 ---


def init_paddle_ocr(use_paddle: bool = True) -> Optional[Any]:
    """初始化PaddleOCR引擎，如果可用且被请求。

    Args:
        use_paddle: 是否尝试初始化PaddleOCR。

    Returns:
        初始化的 PaddleOCR 引擎实例，如果不可用或初始化失败则返回 None。
    """
    if not use_paddle or not PADDLE_OCR_AVAILABLE:
        if use_paddle and not PADDLE_OCR_AVAILABLE:
            logger.warning("请求使用PaddleOCR，但未安装或不可用。将回退。")
        else:
            logger.info("未请求使用PaddleOCR。")
        return None

    try:
        # 简化初始化，使用官方文档推荐的参数
        # 注意：PaddleOCR() 的调用可能比较耗时
        logger.info("开始初始化PaddleOCR引擎...")
        # CHANGE: [2025-04-18] 添加详细错误捕获
        try:
            # 尝试导入 PaddleOCR 类本身，这通常是安全的
            logger.info("PaddleOCR 类导入成功")
            # 分开初始化引擎
            # CHANGE: [2025-06-27] 移除 use_gpu 参数以兼容 PaddleOCR 3.x
            # PaddleOCR 3.x 及以上版本不再支持 `use_gpu` 初始化参数，
            # 而是自动检测环境。在无 GPU 环境下默认使用 CPU。
            # CHANGE: [2025-06-27] 更新初始化参数以匹配新版本并启用文档处理功能
            # CHANGE: [2025-07-29] 指定使用 PP-OCRv4 模型并最终确认参数
            paddle_engine = PaddleOCR(
                ocr_version="PP-OCRv4",  # 指定使用 PP-OCRv4 模型
                lang="ch",  # PP-OCRv4 支持中英文
                use_doc_orientation_classify=False,
                use_doc_unwarping=False,
                # 最终确认：在3.0版中，此初始化参数为最高优先级，False表示完全不加载角度分类模块
                use_textline_orientation=False  
            )
            logger.info("PaddleOCR引擎初始化成功 (PP-OCRv4, CPU模式)。")
            return paddle_engine
        except ImportError as import_err:
            logger.error(f"PaddleOCR 类导入失败: {import_err}", exc_info=True)
            logger.warning("PaddleOCR 类导入失败，将无法使用PaddleOCR。")
            return None
        except Exception as init_err:
            logger.error(f"PaddleOCR 引擎初始化失败: {init_err}", exc_info=True)
            logger.warning("PaddleOCR 引擎初始化失败，将无法使用PaddleOCR。")
            return None
    except Exception as e:  # 捕获意料之外的顶层错误
        logger.error(f"PaddleOCR 初始化过程中发生未知错误: {e}", exc_info=True)
        logger.warning("未知错误导致无法使用 PaddleOCR。")
        return None


# --- OCR 执行函数 ---


def run_tesseract_basic(image: Image.Image, config: str = None) -> str:
    """在单个预处理图像上运行 Tesseract OCR。

    Args:
        image: 经过 Tesseract 优化的预处理图像 (PIL.Image 对象)。
        config: Tesseract 配置字符串。如果为 None，使用默认配置。

    Returns:
        识别出的文本字符串，失败则返回空字符串。
    """
    if not image:
        return ""

    effective_config = config or "--oem 1 --psm 6 -l chi_sim+eng --dpi 150"

    try:
        logger.debug(f"运行 Tesseract (Basic) 使用配置: {effective_config}")
        text = pytesseract.image_to_string(image, config=effective_config)
        result = text.strip()
        logger.debug(f"Tesseract (Basic) 结果片段: {result[:50]}...")
        return result
    except Exception as e:
        logger.error(f"Tesseract OCR (Basic) 失败: {e}")
        return ""


def run_tesseract_enhanced(
    images: List[Image.Image], configs: List[str] = None
) -> List[str]:
    """在多个图像版本上运行 Tesseract OCR (用于增强识别)。

    Args:
        images: 预处理后的图像列表 (PIL.Image 对象)。
        configs: 与图像列表对应的 Tesseract 配置字符串列表。
                 如果为 None，将使用默认配置。

    Returns:
        每个图像识别出的文本列表 (失败则为空字符串)。
    """
    results = []
    if not images:
        return results

    # 如果未提供配置，为每个图像生成默认配置
    if configs is None:
        default_config = "--oem 1 --psm 6 -l chi_sim+eng --dpi 150"
        configs = [default_config] * len(images)
    elif len(configs) != len(images):
        logger.warning(
            f"提供的 Tesseract 配置数量 ({len(configs)}) 与图像数量 ({len(images)}) 不匹配。将尝试使用第一个配置。"
        )
        configs = [configs[0]] * len(images)  # 使用第一个配置填充

    for i, (image, config) in enumerate(zip(images, configs)):
        try:
            logger.debug(
                f"运行 Tesseract (增强尝试 {i+1}/{len(images)}) 使用配置: {config}"
            )
            text = pytesseract.image_to_string(image, config=config)
            results.append(text.strip())
            logger.debug(f"Tesseract (增强尝试 {i+1}) 结果片段: {text[:50]}...")
        except Exception as e:
            logger.error(f"Tesseract OCR 增强尝试 {i+1} 失败: {e}")
            results.append("")  # 添加空字符串表示失败

    return results


def run_paddle_basic(image: Image.Image, paddle_engine: Any) -> str:
    """在单个预处理图像上运行 PaddleOCR。

    Args:
        image: 经过 PaddleOCR 优化的预处理图像 (通常是 RGB)。
        paddle_engine: 已初始化的 PaddleOCR 引擎实例。

    Returns:
        识别出的文本字符串，失败则返回空字符串。
    """
    if not image or not paddle_engine:
        logger.warning("运行 PaddleOCR (Basic) 的输入图像或引擎无效。")
        return ""

    try:
        logger.debug("运行 PaddleOCR (Basic)...")
        # PaddleOCR 需要 NumPy 数组
        img_array = np.array(image)
        # CHANGE: [2025-06-27] 使用官方推荐的 predict 方法名
        result = paddle_engine.predict(img_array)

        # 解析结果 - 适配新版 PaddleOCR 的文档模式输出
        # 新版文档模式下，result 是一个列表，每个元素是对应一页图像结果的字典
        if result and len(result) > 0 and result[0]:
            page_result = result[0]
            # 检查并处理新版的文档模式输出 (字典格式)
            if isinstance(page_result, dict) and 'rec_texts' in page_result:
                # e.g., {'rec_texts': ['text1', 'text2'], ...}
                text_list = page_result.get('rec_texts', [])
                extracted_text = " ".join(text_list)
                logger.debug(f"PaddleOCR (Basic - Doc Mode) 结果片段: {extracted_text[:50]}...")
                return extracted_text.strip()

        logger.debug("PaddleOCR (Basic) 未返回可解析的结果。")
        return ""

    except Exception as e:
        logger.error(f"PaddleOCR (Basic) 失败: {e}", exc_info=True)
        return ""


def run_paddle_enhanced(
    images: List[Image.Image], paddle_engine: Any
) -> List[str]:
    """在多个图像版本上运行 PaddleOCR (用于增强识别)。

    Args:
        images: 预处理后的图像列表 (通常是 RGB)。
        paddle_engine: 已初始化的 PaddleOCR 引擎实例。

    Returns:
        每个图像识别出的文本列表 (失败则为空字符串)。
    """
    ocr_results = []
    if not images or not paddle_engine:
        logger.warning("运行 PaddleOCR (Enhanced) 的输入图像列表或引擎无效。")
        return [""] * len(images or [])

    for i, image in enumerate(images):
        if not image:
            logger.warning(
                f"PaddleOCR (Enhanced) 尝试 {i+1}/{len(images)} 的图像无效，跳过。"
            )
            ocr_results.append("")
            continue

        try:
            logger.debug(f"运行 PaddleOCR (增强尝试 {i+1}/{len(images)})...")
            img_array = np.array(image)
            # CHANGE: [2025-06-27] 使用官方推荐的 predict 方法名
            result = paddle_engine.predict(img_array)

            # 解析结果 - 适配文档模式和旧模式
            if result and len(result) > 0 and result[0]:
                page_result = result[0]
                extracted_text = ""
                # 检查并处理新版的文档模式输出 (字典格式)
                if isinstance(page_result, dict) and 'rec_texts' in page_result:
                    text_list = page_result.get('rec_texts', [])
                    extracted_text = " ".join(text_list)

                if extracted_text:
                    ocr_results.append(extracted_text.strip())
                    logger.debug(
                        f"PaddleOCR (增强尝试 {i+1}) 结果片段: {extracted_text[:50]}..."
                    )
                else:
                    logger.debug(f"PaddleOCR (增强尝试 {i+1}) 未返回可解析的文本。")
                    ocr_results.append("")
            else:
                logger.debug(f"PaddleOCR (增强尝试 {i+1}) 未返回有效结果。")
                ocr_results.append("")

        except Exception as e:
            logger.error(f"PaddleOCR 增强尝试 {i+1} 失败: {e}", exc_info=True)
            ocr_results.append("")  # 添加空字符串表示失败

    return ocr_results


# [这里将添加 run_ocr_on_image 函数]


# --- 页面级 OCR 流程 ---

# [这里将添加 ocr_page_and_extract 函数]


# --- 带控制逻辑的 OCR 调用 ---


def perform_basic_ocr(
    image: Image.Image,
    engine: str,
    ocr_cache: Dict[str, str],
    enable_cache: bool,
    paddle_engine: Optional[Any] = None,
    use_paddle: bool = False,
    config: Optional[str] = None,
    page_num: Optional[int] = None,
) -> str:
    """执行基础OCR，包括缓存检查、引擎选择和回退逻辑。

    Args:
        image: 原始 PIL 图像。
        engine: OCR 引擎选择 ("auto", "paddle", "tesseract")。
        ocr_cache: OCR 结果缓存字典。
        enable_cache: 是否启用缓存。
        paddle_engine: 已初始化的 PaddleOCR 引擎 (如果 use_paddle=True)。
        use_paddle: 是否允许使用 PaddleOCR。
        config: Tesseract 配置字符串 (可选)。
        page_num: 页码 (用于日志和缓存键，可选)。

    Returns:
        识别出的文本字符串。
    """
    if not image:
        return ""

    img_hash = None
    # 缓存检查
    if page_num is not None and enable_cache:
        try:
            img_hash = image_utils.image_hash(image)
            if img_hash in ocr_cache:
                logger.debug(f"第{page_num+1}页OCR结果命中缓存")
                return ocr_cache[img_hash]
        except Exception as hash_e:
            logger.warning(
                f"计算或检查图像哈希失败 {f'(页码:{page_num})' if page_num else ''}: {hash_e}"
            )
            # 即使哈希失败，也继续尝试OCR，但不使用缓存
            enable_cache = False

    text_result = ""
    try:
        # 自动选择或指定PaddleOCR
        should_try_paddle = (
            (engine.lower() == "auto" or engine.lower() == "paddle")
            and use_paddle
            and paddle_engine is not None
        )

        if should_try_paddle:
            # 1. 获取 Paddle 标准预处理图像
            processed_image = image_utils.prepare_standard_image_for_paddle(image)
            if processed_image:
                # 2. 运行基础 Paddle OCR
                text_result = run_paddle_basic(processed_image, paddle_engine)
            else:
                logger.warning(
                    f"无法为 PaddleOCR 生成预处理图像 {f'(页码:{page_num})' if page_num else ''}"
                )

            # 检查Paddle结果，如果为空且是auto模式，则回退到Tesseract
            if not text_result and engine.lower() == "auto":
                logger.info(
                    f"PaddleOCR 未返回有效结果或图像准备失败，自动回退到 Tesseract..."
                )
                # Fallthrough to Tesseract block
                pass  # 继续执行下面的 Tesseract 逻辑
            elif text_result:
                # Paddle 成功 (或指定了Paddle但图像准备失败导致text_result为空)
                pass  # 使用 text_result
            else:
                # 指定了 Paddle，但准备失败或执行失败，返回空
                return ""

        # Tesseract模式 (作为指定模式、回退模式、或 Paddle 失败后的 Fallthrough)
        if not text_result and (
            engine.lower() == "tesseract" or engine.lower() == "auto"
        ):
            # 1. 获取 Tesseract 标准预处理图像
            processed_image = image_utils.prepare_standard_image_for_tesseract(image)
            if processed_image:
                # 2. 运行基础 Tesseract OCR
                text_result = run_tesseract_basic(processed_image, config=config)
            else:
                logger.warning(
                    f"无法为 Tesseract 生成预处理图像 {f'(页码:{page_num})' if page_num else ''}"
                )
                text_result = ""  # 图像准备失败

        # 缓存结果 (如果成功识别出文本且缓存启用)
        if text_result and page_num is not None and enable_cache and img_hash:
            try:
                ocr_cache[img_hash] = text_result
            except Exception as cache_e:
                logger.warning(
                    f"写入OCR缓存失败 {f'(页码:{page_num})' if page_num else ''}: {cache_e}"
                )

        # 记录日志（降低频率）
        if text_result and page_num and page_num % 200 == 0:
            log_engine = "Paddle" if should_try_paddle and text_result else "Tesseract"
            logger.debug(
                f"第{page_num+1}页的 Basic OCR ({log_engine}) 结果片段: {text_result[:50]}..."
            )

        return text_result

    except Exception as e:
        logger.error(
            f"执行基础 OCR 失败{f'(页码:{page_num})' if page_num else ''}: {e}",
            exc_info=True,
        )
        return ""  # 发生意外错误时返回空字符串


def perform_enhanced_ocr(
    image: Image.Image,
    use_paddle: bool,
    paddle_engine: Optional[Any],
    target_text: Optional[str] = None,
    extract_number: bool = False,
    max_attempts: int = 3,
    collect_all_results: bool = False,
    tesseract_configs: Optional[List[str]] = None,  # 用于 Tesseract 的配置列表
) -> Union[bool, List[UnifiedNumberResult]]:
    """执行增强OCR，尝试多种图像处理和OCR配置以提高识别率。

    Args:
        image: 原始 PIL 图像。
        use_paddle: 是否优先使用 PaddleOCR。
        paddle_engine: 已初始化的 PaddleOCR 引擎实例。
        target_text: 用于提前退出的目标文本 (可选)。
        extract_number: 是否尝试提取统一编号。
        max_attempts: 最大尝试次数。
        collect_all_results: 是否收集所有结果 (如果为False，找到target_text即返回True)。
        tesseract_configs: Tesseract 的配置列表 (可选)。

    Returns:
        如果 collect_all_results=False 且找到 target_text，返回 True。
        如果 extract_number=True，返回 UnifiedNumberResult 列表。
        否则返回 False。
    """
    results: List[UnifiedNumberResult] = []
    if not image:
        return [] if extract_number else False

    try:
        if use_paddle and paddle_engine is not None:
            # --- PaddleOCR 增强模式 ---
            logger.debug("执行 PaddleOCR 增强识别...")
            # 1. 获取 Paddle 优化的增强图像列表
            processed_images = image_utils.get_enhanced_images_for_paddle(image)
            if not processed_images:
                logger.warning("无法为 PaddleOCR 生成增强图像列表")
                return [] if extract_number else False

            # 2. 运行增强 Paddle OCR 在多个图像上
            ocr_texts = run_paddle_enhanced(
                processed_images[:max_attempts], paddle_engine
            )

            # 3. 处理结果
            for i, text in enumerate(ocr_texts):
                if not text:
                    if extract_number:
                        results.append(
                            UnifiedNumberResult(None, f"PaddleOCR增强_{i+1} (失败)")
                        )
                    continue

                if target_text and target_text in text:
                    if not collect_all_results:
                        return True

                if extract_number:
                    number = text_utils.extract_unified_number(text, target_text)
                    method_name = f"PaddleOCR增强_{i+1}"
                    results.append(UnifiedNumberResult(number, method_name))

        else:
            # --- Tesseract 增强模式 ---
            logger.debug("执行 Tesseract 增强识别...")
            # 1. 获取 Tesseract 优化的增强图像列表
            processed_images = image_utils.get_enhanced_images_for_tesseract(image)
            if not processed_images:
                logger.warning("无法为 Tesseract 生成增强图像列表")
                return [] if extract_number else False

            # 2. 准备 Tesseract 配置 (如果未提供，使用默认)
            configs = tesseract_configs
            if configs is None:
                default_config = "--oem 1 --psm 6 -l chi_sim+eng --dpi 150"
                configs = [default_config] * max_attempts
            elif len(configs) < max_attempts:
                configs.extend(
                    [configs[-1]] * (max_attempts - len(configs))
                )  # 用最后一个配置填充

            # 3. 运行增强 Tesseract OCR 在多个图像上
            ocr_texts = run_tesseract_enhanced(
                processed_images[:max_attempts], configs[:max_attempts]
            )

            # 4. 处理结果
            for i, text in enumerate(ocr_texts):
                if not text:
                    if extract_number:
                        results.append(
                            UnifiedNumberResult(None, f"Tesseract增强_{i+1} (失败)")
                        )
                    continue

                if target_text and target_text in text:
                    if not collect_all_results:
                        return True

                if extract_number:
                    number = text_utils.extract_unified_number(text, target_text)
                    method_name = f"Tesseract增强_{i+1}"
                    results.append(UnifiedNumberResult(number, method_name))

        # 返回最终结果
        return results if extract_number else False

    except Exception as e:
        logger.error(f"执行增强 OCR 失败: {e}", exc_info=True)
        return [] if extract_number else False


# --- 结果验证 ---

# [这里可以考虑添加 validate_and_choose_number 函数]
